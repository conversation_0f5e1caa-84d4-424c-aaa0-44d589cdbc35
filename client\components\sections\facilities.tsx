"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card } from "@/components/ui/card"
import { ScrollAnimation, StaggeredAnimation, ScaleInOnScroll } from "@/components/ui/scroll-animation"
import { 
  Wifi, 
  Shield, 
  Car, 
  Coffee, 
  Utensils, 
  Shirt, 
  Dumbbell, 
  BookOpen,
  Users,
  Clock,
  Camera,
  Zap,
  Droplets,
  Wind,
  Tv,
  Gamepad2,
  MapPin,
  Phone,
  Headphones,
  Refrigerator,
  Microwave,
  WashingMachine
} from "lucide-react"
import type { Facility } from "@/types"

const facilities: Facility[] = [
  {
    id: "1",
    name: "High-Speed WiFi",
    icon: "Wifi",
    description: "Unlimited high-speed internet access throughout the building with fiber optic connection.",
    available24h: true
  },
  {
    id: "2", 
    name: "24/7 Security",
    icon: "Shield",
    description: "Round-the-clock security with CCTV monitoring and access card system for your safety.",
    available24h: true
  },
  {
    id: "3",
    name: "Parking Area",
    icon: "Car", 
    description: "Secure parking space for motorcycles and bicycles with covered area.",
    available24h: true
  },
  {
    id: "4",
    name: "Common Lounge",
    icon: "Coffee",
    description: "Comfortable shared lounge area perfect for socializing and relaxation.",
    available24h: false
  },
  {
    id: "5",
    name: "Shared Kitchen",
    icon: "Utensils",
    description: "Fully equipped communal kitchen with modern appliances and dining area.",
    available24h: false
  },
  {
    id: "6",
    name: "Laundry Service",
    icon: "WashingMachine",
    description: "Self-service laundry facilities with washing machines and dryers.",
    available24h: false
  },
  {
    id: "7",
    name: "Fitness Corner",
    icon: "Dumbbell",
    description: "Basic fitness equipment and exercise area for maintaining your health.",
    available24h: false
  },
  {
    id: "8",
    name: "Study Room",
    icon: "BookOpen", 
    description: "Quiet study spaces with proper lighting and comfortable seating.",
    available24h: true
  },
  {
    id: "9",
    name: "Entertainment Room",
    icon: "Tv",
    description: "Recreation area with TV, gaming console, and comfortable seating.",
    available24h: false
  },
  {
    id: "10",
    name: "Air Conditioning",
    icon: "Wind",
    description: "Individual AC units in each room for optimal comfort year-round.",
    available24h: true
  },
  {
    id: "11",
    name: "Water Heater",
    icon: "Droplets",
    description: "Hot water available in all bathrooms for comfortable bathing experience.",
    available24h: true
  },
  {
    id: "12",
    name: "Housekeeping",
    icon: "Users",
    description: "Regular cleaning service for common areas and room maintenance.",
    available24h: false
  }
]

const iconComponents = {
  Wifi,
  Shield, 
  Car,
  Coffee,
  Utensils,
  WashingMachine,
  Dumbbell,
  BookOpen,
  Tv,
  Wind,
  Droplets,
  Users
}

interface FacilityCardProps {
  facility: Facility;
  index: number;
}

function FacilityCard({ facility, index }: FacilityCardProps) {
  const IconComponent = iconComponents[facility.icon as keyof typeof iconComponents] || Wifi

  return (
    <ScaleInOnScroll delay={index * 0.1}>
      <Card 
        hover 
        className="p-6 h-full group cursor-pointer transition-all duration-300 hover:shadow-lg"
      >
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Icon */}
          <motion.div
            className="relative p-4 bg-blue-50 rounded-2xl group-hover:bg-blue-100 transition-colors duration-300"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            <IconComponent className="h-8 w-8 text-blue-600" />
            {facility.available24h && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <Clock className="h-2 w-2 text-white" />
              </div>
            )}
          </motion.div>

          {/* Content */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {facility.name}
            </h3>
            <p className="text-sm text-gray-600 leading-relaxed">
              {facility.description}
            </p>
          </div>

          {/* Availability Badge */}
          <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
            facility.available24h 
              ? 'bg-green-100 text-green-800' 
              : 'bg-blue-100 text-blue-800'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              facility.available24h ? 'bg-green-500' : 'bg-blue-500'
            }`} />
            {facility.available24h ? '24/7 Available' : 'Scheduled Hours'}
          </div>
        </div>
      </Card>
    </ScaleInOnScroll>
  )
}

export function FacilitiesSection() {
  const [selectedCategory, setSelectedCategory] = React.useState<'all' | 'essential' | 'comfort' | 'recreation'>('all')

  const categories = [
    { id: 'all', label: 'All Facilities', count: facilities.length },
    { id: 'essential', label: 'Essential', count: 6 },
    { id: 'comfort', label: 'Comfort', count: 4 },
    { id: 'recreation', label: 'Recreation', count: 2 }
  ]

  const getFilteredFacilities = () => {
    switch (selectedCategory) {
      case 'essential':
        return facilities.filter(f => ['1', '2', '3', '8', '10', '11'].includes(f.id))
      case 'comfort':
        return facilities.filter(f => ['4', '5', '6', '12'].includes(f.id))
      case 'recreation':
        return facilities.filter(f => ['7', '9'].includes(f.id))
      default:
        return facilities
    }
  }

  return (
    <section id="facilities" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <ScrollAnimation animation="fadeIn" className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-4"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              World-Class Facilities
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Experience comfortable living with our comprehensive range of modern amenities 
              designed to make your stay convenient and enjoyable.
            </p>
          </motion.div>
        </ScrollAnimation>

        {/* Category Filter */}
        <ScrollAnimation animation="slideUp" className="flex justify-center mb-12">
          <div className="flex flex-wrap gap-2 p-1 bg-gray-100 rounded-lg">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id as any)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-blue-600 hover:bg-white'
                }`}
              >
                {category.label}
                <span className="ml-2 text-xs opacity-75">({category.count})</span>
              </button>
            ))}
          </div>
        </ScrollAnimation>

        {/* Facilities Grid */}
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          layout
        >
          {getFilteredFacilities().map((facility, index) => (
            <motion.div
              key={facility.id}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <FacilityCard facility={facility} index={index} />
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info */}
        <ScrollAnimation animation="fadeIn" className="mt-16">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-8">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="space-y-2"
              >
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">24/7 Support</h3>
                <p className="text-sm text-gray-600">
                  Our staff is available round the clock for any assistance you need.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-2"
              >
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Safe & Secure</h3>
                <p className="text-sm text-gray-600">
                  Advanced security systems ensure your safety and peace of mind.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="space-y-2"
              >
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Community</h3>
                <p className="text-sm text-gray-600">
                  Join a vibrant community of like-minded residents and make lasting friendships.
                </p>
              </motion.div>
            </div>
          </div>
        </ScrollAnimation>
      </div>
    </section>
  )
}
