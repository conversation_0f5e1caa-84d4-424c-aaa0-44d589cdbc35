"use client"

import * as React from "react"
import dynamic from "next/dynamic"
import { motion } from "framer-motion"
import { Navigation } from "./navigation"
import { Footer } from "./footer"
import { Loading, LoadingOverlay } from "./ui/loading"
import { prefersReducedMotion } from "@/utils/accessibility"

// Lazy load sections for better performance
const HeroSection = dynamic(() => import("./sections/hero").then(mod => ({ default: mod.HeroSection })), {
  loading: () => <Loading variant="skeleton" className="min-h-screen" />,
  ssr: true
})

const RoomsSection = dynamic(() => import("./sections/rooms").then(mod => ({ default: mod.RoomsSection })), {
  loading: () => <Loading variant="skeleton" className="h-96" />,
  ssr: false
})

const FacilitiesSection = dynamic(() => import("./sections/facilities").then(mod => ({ default: mod.FacilitiesSection })), {
  loading: () => <Loading variant="skeleton" className="h-96" />,
  ssr: false
})

const TestimonialsSection = dynamic(() => import("./sections/testimonials").then(mod => ({ default: mod.TestimonialsSection })), {
  loading: () => <Loading variant="skeleton" className="h-96" />,
  ssr: false
})

interface BoardingHouseLandingProps {
  className?: string;
}

export function BoardingHouseLanding({ className }: BoardingHouseLandingProps) {
  const [isLoading, setIsLoading] = React.useState(true)
  const [loadedSections, setLoadedSections] = React.useState<Set<string>>(new Set())
  const [reducedMotion, setReducedMotion] = React.useState(false)

  // Check for reduced motion preference
  React.useEffect(() => {
    setReducedMotion(prefersReducedMotion())
    
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const handleChange = () => setReducedMotion(mediaQuery.matches)
    
    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Handle initial loading
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Preload critical images
  React.useEffect(() => {
    const criticalImages = [
      '/images/hero-1.jpg',
      '/images/hero-2.jpg',
      '/images/hero-3.jpg'
    ]

    criticalImages.forEach(src => {
      const img = new Image()
      img.src = src
    })
  }, [])

  // Intersection Observer for lazy loading sections
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const sectionId = entry.target.id
            setLoadedSections(prev => new Set([...prev, sectionId]))
          }
        })
      },
      {
        rootMargin: '100px 0px',
        threshold: 0.1
      }
    )

    // Observe all sections
    const sections = document.querySelectorAll('section[id]')
    sections.forEach(section => observer.observe(section))

    return () => observer.disconnect()
  }, [])

  // Skip link for accessibility
  React.useEffect(() => {
    const skipLink = document.createElement('a')
    skipLink.href = '#main-content'
    skipLink.textContent = 'Skip to main content'
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded'
    
    document.body.insertBefore(skipLink, document.body.firstChild)
    
    return () => {
      if (document.body.contains(skipLink)) {
        document.body.removeChild(skipLink)
      }
    }
  }, [])

  return (
    <div className={`min-h-screen bg-white ${className}`}>
      {/* Loading Overlay */}
      <LoadingOverlay 
        isVisible={isLoading} 
        text="Welcome to BoardingHouse"
      />

      {/* Navigation */}
      <Navigation />

      {/* Main Content */}
      <main id="main-content" role="main">
        {/* Hero Section */}
        <React.Suspense fallback={<Loading variant="skeleton" className="min-h-screen" />}>
          <HeroSection />
        </React.Suspense>

        {/* Rooms Section */}
        <React.Suspense fallback={<Loading variant="skeleton" className="h-96" />}>
          <RoomsSection />
        </React.Suspense>

        {/* Facilities Section */}
        <React.Suspense fallback={<Loading variant="skeleton" className="h-96" />}>
          <FacilitiesSection />
        </React.Suspense>

        {/* Testimonials Section */}
        <React.Suspense fallback={<Loading variant="skeleton" className="h-96" />}>
          <TestimonialsSection />
        </React.Suspense>
      </main>

      {/* Footer */}
      <Footer />

      {/* Performance Monitoring (Development Only) */}
      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
    </div>
  )
}

// Performance monitoring component for development
function PerformanceMonitor() {
  const [metrics, setMetrics] = React.useState<any>(null)

  React.useEffect(() => {
    if (typeof window === 'undefined' || !('performance' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        console.log(`Performance: ${entry.name} - ${entry.duration}ms`)
      })
    })

    observer.observe({ entryTypes: ['measure', 'navigation'] })

    // Monitor memory usage if available
    if ('memory' in performance) {
      const updateMemory = () => {
        const memory = (performance as any).memory
        setMetrics({
          usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576),
          totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576),
          jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576)
        })
      }

      updateMemory()
      const interval = setInterval(updateMemory, 5000)

      return () => {
        clearInterval(interval)
        observer.disconnect()
      }
    }

    return () => observer.disconnect()
  }, [])

  if (!metrics) return null

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs z-50">
      <div>Memory: {metrics.usedJSHeapSize}MB / {metrics.totalJSHeapSize}MB</div>
      <div>Limit: {metrics.jsHeapSizeLimit}MB</div>
    </div>
  )
}

// Error Boundary for better error handling
export class BoardingHouseErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('BoardingHouse Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-gray-900">Something went wrong</h1>
            <p className="text-gray-600">We're sorry, but something unexpected happened.</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// HOC for performance optimization
export function withPerformanceOptimization<P extends object>(
  Component: React.ComponentType<P>
) {
  const OptimizedComponent = React.memo((props: P) => {
    return <Component {...props} />
  })

  OptimizedComponent.displayName = `withPerformanceOptimization(${Component.displayName || Component.name})`
  
  return OptimizedComponent
}

// Custom hook for intersection observer
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = React.useState(false)

  React.useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)
    return () => observer.unobserve(element)
  }, [elementRef, options])

  return isIntersecting
}

// Service Worker registration for PWA capabilities
export function registerServiceWorker() {
  if (
    typeof window !== 'undefined' &&
    'serviceWorker' in navigator &&
    process.env.NODE_ENV === 'production'
  ) {
    window.addEventListener('load', async () => {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('SW registered: ', registration)
      } catch (registrationError) {
        console.log('SW registration failed: ', registrationError)
      }
    })
  }
}
