(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/ui/loading.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Loading": ()=>Loading,
    "LoadingDots": ()=>LoadingDots,
    "LoadingOverlay": ()=>LoadingOverlay,
    "LoadingSkeleton": ()=>LoadingSkeleton,
    "LoadingSpinner": ()=>LoadingSpinner,
    "SkeletonAvatar": ()=>SkeletonAvatar,
    "SkeletonCard": ()=>SkeletonCard,
    "SkeletonText": ()=>SkeletonText
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$animations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/animations.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const loadingVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("flex items-center justify-center", {
    variants: {
        variant: {
            spinner: "",
            skeleton: "animate-pulse bg-muted rounded",
            dots: "space-x-1"
        },
        size: {
            sm: "h-4 w-4",
            md: "h-6 w-6",
            lg: "h-8 w-8"
        }
    },
    defaultVariants: {
        variant: "spinner",
        size: "md"
    }
});
// Spinner Loading Component
function SpinnerLoading(param) {
    let { size, className } = param;
    const sizeClasses = {
        sm: "h-4 w-4 border-2",
        md: "h-6 w-6 border-2",
        lg: "h-8 w-8 border-3"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("rounded-full border-current border-t-transparent", sizeClasses[size || "md"], className),
        variants: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$animations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["spinnerVariants"],
        animate: "animate",
        "aria-hidden": "true"
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
_c = SpinnerLoading;
// Skeleton Loading Component
function SkeletonLoading(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("animate-pulse rounded-md bg-muted", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
}
_c1 = SkeletonLoading;
// Dots Loading Component
function DotsLoading(param) {
    let { size, className } = param;
    const dotSizes = {
        sm: "h-1 w-1",
        md: "h-2 w-2",
        lg: "h-3 w-3"
    };
    const dotSize = dotSizes[size || "md"];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex space-x-1", className),
        children: [
            0,
            1,
            2
        ].map((index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-current rounded-full", dotSize),
                animate: {
                    scale: [
                        1,
                        1.2,
                        1
                    ],
                    opacity: [
                        0.7,
                        1,
                        0.7
                    ]
                },
                transition: {
                    duration: 0.6,
                    repeat: Infinity,
                    delay: index * 0.2
                },
                "aria-hidden": "true"
            }, index, false, {
                fileName: "[project]/components/ui/loading.tsx",
                lineNumber: 82,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
}
_c2 = DotsLoading;
function Loading(param) {
    let { variant = "spinner", size = "md", text, className } = param;
    const renderLoading = ()=>{
        switch(variant){
            case "skeleton":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(loadingVariants({
                        size
                    }), className)
                }, void 0, false, {
                    fileName: "[project]/components/ui/loading.tsx",
                    lineNumber: 106,
                    columnNumber: 16
                }, this);
            case "dots":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DotsLoading, {
                    size: size,
                    className: className
                }, void 0, false, {
                    fileName: "[project]/components/ui/loading.tsx",
                    lineNumber: 108,
                    columnNumber: 16
                }, this);
            case "spinner":
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SpinnerLoading, {
                    size: size,
                    className: className
                }, void 0, false, {
                    fileName: "[project]/components/ui/loading.tsx",
                    lineNumber: 111,
                    columnNumber: 16
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col items-center justify-center gap-2", className),
        children: [
            renderLoading(),
            text && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-muted-foreground animate-pulse",
                "aria-live": "polite",
                children: text
            }, void 0, false, {
                fileName: "[project]/components/ui/loading.tsx",
                lineNumber: 119,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this);
}
_c3 = Loading;
function LoadingSpinner(param) {
    let { size = "md", className } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SpinnerLoading, {
        size: size,
        className: className
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 129,
        columnNumber: 10
    }, this);
}
_c4 = LoadingSpinner;
function LoadingSkeleton(param) {
    let { className, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
        className: className,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 133,
        columnNumber: 10
    }, this);
}
_c5 = LoadingSkeleton;
function LoadingDots(param) {
    let { size = "md", className } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DotsLoading, {
        size: size,
        className: className
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 137,
        columnNumber: 10
    }, this);
}
_c6 = LoadingDots;
function SkeletonText(param) {
    let { lines = 3, className } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("space-y-2", className),
        children: Array.from({
            length: lines
        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("h-4", index === lines - 1 ? "w-3/4" : "w-full" // Last line is shorter
                )
            }, index, false, {
                fileName: "[project]/components/ui/loading.tsx",
                lineNumber: 145,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_c7 = SkeletonText;
function SkeletonCard(param) {
    let { className } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("space-y-4 p-4", className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
                className: "h-48 w-full"
            }, void 0, false, {
                fileName: "[project]/components/ui/loading.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
                        className: "h-4 w-3/4"
                    }, void 0, false, {
                        fileName: "[project]/components/ui/loading.tsx",
                        lineNumber: 162,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
                        className: "h-4 w-1/2"
                    }, void 0, false, {
                        fileName: "[project]/components/ui/loading.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ui/loading.tsx",
                lineNumber: 161,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 159,
        columnNumber: 5
    }, this);
}
_c8 = SkeletonCard;
function SkeletonAvatar(param) {
    let { size = "md", className } = param;
    const sizeClasses = {
        sm: "h-8 w-8",
        md: "h-10 w-10",
        lg: "h-12 w-12"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoading, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("rounded-full", sizeClasses[size], className)
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 177,
        columnNumber: 5
    }, this);
}
_c9 = SkeletonAvatar;
function LoadingOverlay(param) {
    let { isVisible, text = "Loading...", className } = param;
    if (!isVisible) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm", className),
        initial: {
            opacity: 0
        },
        animate: {
            opacity: 1
        },
        exit: {
            opacity: 0
        },
        transition: {
            duration: 0.2
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center space-y-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingSpinner, {
                    size: "lg"
                }, void 0, false, {
                    fileName: "[project]/components/ui/loading.tsx",
                    lineNumber: 207,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-lg font-medium",
                    "aria-live": "polite",
                    children: text
                }, void 0, false, {
                    fileName: "[project]/components/ui/loading.tsx",
                    lineNumber: 208,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/ui/loading.tsx",
            lineNumber: 206,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/loading.tsx",
        lineNumber: 196,
        columnNumber: 5
    }, this);
}
_c10 = LoadingOverlay;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;
__turbopack_context__.k.register(_c, "SpinnerLoading");
__turbopack_context__.k.register(_c1, "SkeletonLoading");
__turbopack_context__.k.register(_c2, "DotsLoading");
__turbopack_context__.k.register(_c3, "Loading");
__turbopack_context__.k.register(_c4, "LoadingSpinner");
__turbopack_context__.k.register(_c5, "LoadingSkeleton");
__turbopack_context__.k.register(_c6, "LoadingDots");
__turbopack_context__.k.register(_c7, "SkeletonText");
__turbopack_context__.k.register(_c8, "SkeletonCard");
__turbopack_context__.k.register(_c9, "SkeletonAvatar");
__turbopack_context__.k.register(_c10, "LoadingOverlay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/accessibility.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Accessibility utility functions for WCAG 2.1 AA compliance
// Focus management utilities
__turbopack_context__.s({
    "addFocusVisiblePolyfill": ()=>addFocusVisiblePolyfill,
    "announceToScreenReader": ()=>announceToScreenReader,
    "createSkipLink": ()=>createSkipLink,
    "generateId": ()=>generateId,
    "getAriaAttributes": ()=>getAriaAttributes,
    "getSemanticRole": ()=>getSemanticRole,
    "getTextSizeClass": ()=>getTextSizeClass,
    "getValidationMessage": ()=>getValidationMessage,
    "handleEscapeKey": ()=>handleEscapeKey,
    "hasGoodContrast": ()=>hasGoodContrast,
    "isActionKey": ()=>isActionKey,
    "isNavigationKey": ()=>isNavigationKey,
    "prefersHighContrast": ()=>prefersHighContrast,
    "prefersReducedMotion": ()=>prefersReducedMotion,
    "trapFocus": ()=>trapFocus
});
const trapFocus = (element)=>{
    const focusableElements = element.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const firstFocusableElement = focusableElements[0];
    const lastFocusableElement = focusableElements[focusableElements.length - 1];
    const handleTabKey = (e)=>{
        if (e.key !== 'Tab') return;
        if (e.shiftKey) {
            if (document.activeElement === firstFocusableElement) {
                lastFocusableElement.focus();
                e.preventDefault();
            }
        } else {
            if (document.activeElement === lastFocusableElement) {
                firstFocusableElement.focus();
                e.preventDefault();
            }
        }
    };
    element.addEventListener('keydown', handleTabKey);
    firstFocusableElement === null || firstFocusableElement === void 0 ? void 0 : firstFocusableElement.focus();
    return ()=>{
        element.removeEventListener('keydown', handleTabKey);
    };
};
const handleEscapeKey = (callback)=>{
    const handleKeyDown = (e)=>{
        if (e.key === 'Escape') {
            callback();
        }
    };
    document.addEventListener('keydown', handleKeyDown);
    return ()=>{
        document.removeEventListener('keydown', handleKeyDown);
    };
};
const generateId = function() {
    let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'id';
    return "".concat(prefix, "-").concat(Math.random().toString(36).substr(2, 9));
};
const announceToScreenReader = function(message) {
    let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'polite';
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.setAttribute('class', 'sr-only');
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(()=>{
        document.body.removeChild(announcement);
    }, 1000);
};
const hasGoodContrast = (foreground, background)=>{
    // This is a simplified version - in production, use a proper color contrast library
    // For now, we'll assume our design system colors meet WCAG AA standards
    return true;
};
const createSkipLink = function(targetId) {
    let text = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'Skip to main content';
    const skipLink = document.createElement('a');
    skipLink.href = "#".concat(targetId);
    skipLink.textContent = text;
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded';
    return skipLink;
};
const isNavigationKey = (key)=>{
    return [
        'ArrowUp',
        'ArrowDown',
        'ArrowLeft',
        'ArrowRight',
        'Home',
        'End'
    ].includes(key);
};
const isActionKey = (key)=>{
    return [
        'Enter',
        ' '
    ].includes(key);
};
const getAriaAttributes = (props)=>{
    const attributes = {};
    if (props.expanded !== undefined) attributes['aria-expanded'] = props.expanded;
    if (props.selected !== undefined) attributes['aria-selected'] = props.selected;
    if (props.disabled !== undefined) attributes['aria-disabled'] = props.disabled;
    if (props.required !== undefined) attributes['aria-required'] = props.required;
    if (props.invalid !== undefined) attributes['aria-invalid'] = props.invalid;
    if (props.describedBy) attributes['aria-describedby'] = props.describedBy;
    if (props.labelledBy) attributes['aria-labelledby'] = props.labelledBy;
    if (props.controls) attributes['aria-controls'] = props.controls;
    if (props.owns) attributes['aria-owns'] = props.owns;
    return attributes;
};
const addFocusVisiblePolyfill = ()=>{
    // Simple focus-visible polyfill for older browsers
    let hadKeyboardEvent = true;
    const keyboardThrottleTimeout = 100;
    let keyboardThrottleTimeoutID = 0;
    const pointerInitialPress = (e)=>{
        if (e.pointerType === 'mouse' || e.pointerType === 'touch') {
            hadKeyboardEvent = false;
        }
    };
    const onKeyDown = (e)=>{
        if (e.metaKey || e.altKey || e.ctrlKey) {
            return;
        }
        hadKeyboardEvent = true;
        clearTimeout(keyboardThrottleTimeoutID);
        keyboardThrottleTimeoutID = window.setTimeout(()=>{
            hadKeyboardEvent = false;
        }, keyboardThrottleTimeout);
    };
    const onFocus = (e)=>{
        const target = e.target;
        if (hadKeyboardEvent || target.matches(':focus-visible')) {
            target.classList.add('focus-visible');
        }
    };
    const onBlur = (e)=>{
        const target = e.target;
        target.classList.remove('focus-visible');
    };
    document.addEventListener('keydown', onKeyDown, true);
    document.addEventListener('pointerdown', pointerInitialPress, true);
    document.addEventListener('focus', onFocus, true);
    document.addEventListener('blur', onBlur, true);
};
const prefersReducedMotion = ()=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};
const prefersHighContrast = ()=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    return window.matchMedia('(prefers-contrast: high)').matches;
};
const getTextSizeClass = (size)=>{
    const sizes = {
        sm: 'text-sm',
        base: 'text-base',
        lg: 'text-lg',
        xl: 'text-xl'
    };
    return sizes[size];
};
const getSemanticRole = (element)=>{
    const roles = {
        'nav': 'navigation',
        'main': 'main',
        'aside': 'complementary',
        'section': 'region',
        'article': 'article',
        'header': 'banner',
        'footer': 'contentinfo'
    };
    return roles[element] || '';
};
const getValidationMessage = (field, error)=>{
    var _messages_field;
    const messages = {
        email: {
            required: 'Email address is required',
            invalid: 'Please enter a valid email address'
        },
        phone: {
            required: 'Phone number is required',
            invalid: 'Please enter a valid phone number'
        },
        name: {
            required: 'Name is required',
            minLength: 'Name must be at least 2 characters long'
        }
    };
    return ((_messages_field = messages[field]) === null || _messages_field === void 0 ? void 0 : _messages_field[error]) || 'This field is invalid';
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/boarding-house-landing.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BoardingHouseErrorBoundary": ()=>BoardingHouseErrorBoundary,
    "BoardingHouseLanding": ()=>BoardingHouseLanding,
    "registerServiceWorker": ()=>registerServiceWorker,
    "useIntersectionObserver": ()=>useIntersectionObserver,
    "withPerformanceOptimization": ()=>withPerformanceOptimization
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$navigation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/navigation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/loading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$accessibility$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/accessibility.ts [app-client] (ecmascript)");
;
;
;
;
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
// Lazy load sections for better performance
const HeroSection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i).then((mod)=>({
            default: mod.HeroSection
        })), {
    loadableGenerated: {
        modules: [
            "[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
            variant: "skeleton",
            className: "min-h-screen"
        }, void 0, false, {
            fileName: "[project]/components/boarding-house-landing.tsx",
            lineNumber: 13,
            columnNumber: 18
        }, ("TURBOPACK compile-time value", void 0)),
    ssr: true
});
_c = HeroSection;
const RoomsSection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i).then((mod)=>({
            default: mod.RoomsSection
        })), {
    loadableGenerated: {
        modules: [
            "[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
            variant: "skeleton",
            className: "h-96"
        }, void 0, false, {
            fileName: "[project]/components/boarding-house-landing.tsx",
            lineNumber: 18,
            columnNumber: 18
        }, ("TURBOPACK compile-time value", void 0)),
    ssr: false
});
_c1 = RoomsSection;
const FacilitiesSection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i).then((mod)=>({
            default: mod.FacilitiesSection
        })), {
    loadableGenerated: {
        modules: [
            "[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
            variant: "skeleton",
            className: "h-96"
        }, void 0, false, {
            fileName: "[project]/components/boarding-house-landing.tsx",
            lineNumber: 23,
            columnNumber: 18
        }, ("TURBOPACK compile-time value", void 0)),
    ssr: false
});
_c2 = FacilitiesSection;
const TestimonialsSection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i).then((mod)=>({
            default: mod.TestimonialsSection
        })), {
    loadableGenerated: {
        modules: [
            "[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
            variant: "skeleton",
            className: "h-96"
        }, void 0, false, {
            fileName: "[project]/components/boarding-house-landing.tsx",
            lineNumber: 28,
            columnNumber: 18
        }, ("TURBOPACK compile-time value", void 0)),
    ssr: false
});
_c3 = TestimonialsSection;
function BoardingHouseLanding(param) {
    let { className } = param;
    _s();
    const [isLoading, setIsLoading] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](true);
    const [loadedSections, setLoadedSections] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](new Set());
    const [reducedMotion, setReducedMotion] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](false);
    // Check for reduced motion preference
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "BoardingHouseLanding.useEffect": ()=>{
            setReducedMotion((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$accessibility$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prefersReducedMotion"])());
            const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            const handleChange = {
                "BoardingHouseLanding.useEffect.handleChange": ()=>setReducedMotion(mediaQuery.matches)
            }["BoardingHouseLanding.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "BoardingHouseLanding.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["BoardingHouseLanding.useEffect"];
        }
    }["BoardingHouseLanding.useEffect"], []);
    // Handle initial loading
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "BoardingHouseLanding.useEffect": ()=>{
            const timer = setTimeout({
                "BoardingHouseLanding.useEffect.timer": ()=>{
                    setIsLoading(false);
                }
            }["BoardingHouseLanding.useEffect.timer"], 1000);
            return ({
                "BoardingHouseLanding.useEffect": ()=>clearTimeout(timer)
            })["BoardingHouseLanding.useEffect"];
        }
    }["BoardingHouseLanding.useEffect"], []);
    // Preload critical images
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "BoardingHouseLanding.useEffect": ()=>{
            const criticalImages = [
                '/images/hero-1.jpg',
                '/images/hero-2.jpg',
                '/images/hero-3.jpg'
            ];
            criticalImages.forEach({
                "BoardingHouseLanding.useEffect": (src)=>{
                    const img = new Image();
                    img.src = src;
                }
            }["BoardingHouseLanding.useEffect"]);
        }
    }["BoardingHouseLanding.useEffect"], []);
    // Intersection Observer for lazy loading sections
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "BoardingHouseLanding.useEffect": ()=>{
            const observer = new IntersectionObserver({
                "BoardingHouseLanding.useEffect": (entries)=>{
                    entries.forEach({
                        "BoardingHouseLanding.useEffect": (entry)=>{
                            if (entry.isIntersecting) {
                                const sectionId = entry.target.id;
                                setLoadedSections({
                                    "BoardingHouseLanding.useEffect": (prev)=>new Set([
                                            ...prev,
                                            sectionId
                                        ])
                                }["BoardingHouseLanding.useEffect"]);
                            }
                        }
                    }["BoardingHouseLanding.useEffect"]);
                }
            }["BoardingHouseLanding.useEffect"], {
                rootMargin: '100px 0px',
                threshold: 0.1
            });
            // Observe all sections
            const sections = document.querySelectorAll('section[id]');
            sections.forEach({
                "BoardingHouseLanding.useEffect": (section)=>observer.observe(section)
            }["BoardingHouseLanding.useEffect"]);
            return ({
                "BoardingHouseLanding.useEffect": ()=>observer.disconnect()
            })["BoardingHouseLanding.useEffect"];
        }
    }["BoardingHouseLanding.useEffect"], []);
    // Skip link for accessibility
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "BoardingHouseLanding.useEffect": ()=>{
            const skipLink = document.createElement('a');
            skipLink.href = '#main-content';
            skipLink.textContent = 'Skip to main content';
            skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded';
            document.body.insertBefore(skipLink, document.body.firstChild);
            return ({
                "BoardingHouseLanding.useEffect": ()=>{
                    if (document.body.contains(skipLink)) {
                        document.body.removeChild(skipLink);
                    }
                }
            })["BoardingHouseLanding.useEffect"];
        }
    }["BoardingHouseLanding.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-white ".concat(className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingOverlay"], {
                isVisible: isLoading,
                text: "Welcome to BoardingHouse"
            }, void 0, false, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$navigation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Navigation"], {}, void 0, false, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 124,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                id: "main-content",
                role: "main",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
                            variant: "skeleton",
                            className: "min-h-screen"
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 129,
                            columnNumber: 35
                        }, void 0),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(HeroSection, {}, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 130,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/boarding-house-landing.tsx",
                        lineNumber: 129,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
                            variant: "skeleton",
                            className: "h-96"
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 134,
                            columnNumber: 35
                        }, void 0),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(RoomsSection, {}, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/boarding-house-landing.tsx",
                        lineNumber: 134,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
                            variant: "skeleton",
                            className: "h-96"
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 139,
                            columnNumber: 35
                        }, void 0),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FacilitiesSection, {}, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 140,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/boarding-house-landing.tsx",
                        lineNumber: 139,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
                            variant: "skeleton",
                            className: "h-96"
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 144,
                            columnNumber: 35
                        }, void 0),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TestimonialsSection, {}, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 145,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/boarding-house-landing.tsx",
                        lineNumber: 144,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 127,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Footer"], {}, void 0, false, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 150,
                columnNumber: 7
            }, this),
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PerformanceMonitor, {}, void 0, false, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 153,
                columnNumber: 50
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/boarding-house-landing.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this);
}
_s(BoardingHouseLanding, "1TKx+/2njqjiHuLPBNS1YybB8tM=");
_c4 = BoardingHouseLanding;
// Performance monitoring component for development
function PerformanceMonitor() {
    _s1();
    const [metrics, setMetrics] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](null);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "PerformanceMonitor.useEffect": ()=>{
            if ("object" === 'undefined' || !('performance' in window)) return;
            const observer = new PerformanceObserver({
                "PerformanceMonitor.useEffect": (list)=>{
                    const entries = list.getEntries();
                    entries.forEach({
                        "PerformanceMonitor.useEffect": (entry)=>{
                            console.log("Performance: ".concat(entry.name, " - ").concat(entry.duration, "ms"));
                        }
                    }["PerformanceMonitor.useEffect"]);
                }
            }["PerformanceMonitor.useEffect"]);
            observer.observe({
                entryTypes: [
                    'measure',
                    'navigation'
                ]
            });
            // Monitor memory usage if available
            if ('memory' in performance) {
                const updateMemory = {
                    "PerformanceMonitor.useEffect.updateMemory": ()=>{
                        const memory = performance.memory;
                        setMetrics({
                            usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576),
                            totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576),
                            jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576)
                        });
                    }
                }["PerformanceMonitor.useEffect.updateMemory"];
                updateMemory();
                const interval = setInterval(updateMemory, 5000);
                return ({
                    "PerformanceMonitor.useEffect": ()=>{
                        clearInterval(interval);
                        observer.disconnect();
                    }
                })["PerformanceMonitor.useEffect"];
            }
            return ({
                "PerformanceMonitor.useEffect": ()=>observer.disconnect()
            })["PerformanceMonitor.useEffect"];
        }
    }["PerformanceMonitor.useEffect"], []);
    if (!metrics) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs z-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    "Memory: ",
                    metrics.usedJSHeapSize,
                    "MB / ",
                    metrics.totalJSHeapSize,
                    "MB"
                ]
            }, void 0, true, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 201,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    "Limit: ",
                    metrics.jsHeapSizeLimit,
                    "MB"
                ]
            }, void 0, true, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 202,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/boarding-house-landing.tsx",
        lineNumber: 200,
        columnNumber: 5
    }, this);
}
_s1(PerformanceMonitor, "fkAZCjMJDoW80OvZsNTeiaw81+c=");
_c5 = PerformanceMonitor;
class BoardingHouseErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"] {
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('BoardingHouse Error:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-2xl font-bold text-gray-900",
                            children: "Something went wrong"
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 230,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600",
                            children: "We're sorry, but something unexpected happened."
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 231,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>window.location.reload(),
                            className: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",
                            children: "Reload Page"
                        }, void 0, false, {
                            fileName: "[project]/components/boarding-house-landing.tsx",
                            lineNumber: 232,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/boarding-house-landing.tsx",
                    lineNumber: 229,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/boarding-house-landing.tsx",
                lineNumber: 228,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
    constructor(props){
        super(props);
        this.state = {
            hasError: false
        };
    }
}
function withPerformanceOptimization(Component) {
    const OptimizedComponent = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"]((props)=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/components/boarding-house-landing.tsx",
            lineNumber: 252,
            columnNumber: 12
        }, this);
    });
    OptimizedComponent.displayName = "withPerformanceOptimization(".concat(Component.displayName || Component.name, ")");
    return OptimizedComponent;
}
function useIntersectionObserver(elementRef) {
    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    _s2();
    const [isIntersecting, setIsIntersecting] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](false);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "useIntersectionObserver.useEffect": ()=>{
            const element = elementRef.current;
            if (!element) return;
            const observer = new IntersectionObserver({
                "useIntersectionObserver.useEffect": (param)=>{
                    let [entry] = param;
                    return setIsIntersecting(entry.isIntersecting);
                }
            }["useIntersectionObserver.useEffect"], {
                threshold: 0.1,
                rootMargin: '50px',
                ...options
            });
            observer.observe(element);
            return ({
                "useIntersectionObserver.useEffect": ()=>observer.unobserve(element)
            })["useIntersectionObserver.useEffect"];
        }
    }["useIntersectionObserver.useEffect"], [
        elementRef,
        options
    ]);
    return isIntersecting;
}
_s2(useIntersectionObserver, "AlYpBYKL+Qmn8I+76+SVWywhgtA=");
function registerServiceWorker() {
    if ("object" !== 'undefined' && 'serviceWorker' in navigator && ("TURBOPACK compile-time value", "development") === 'production') //TURBOPACK unreachable
    ;
}
var _c, _c1, _c2, _c3, _c4, _c5;
__turbopack_context__.k.register(_c, "HeroSection");
__turbopack_context__.k.register(_c1, "RoomsSection");
__turbopack_context__.k.register(_c2, "FacilitiesSection");
__turbopack_context__.k.register(_c3, "TestimonialsSection");
__turbopack_context__.k.register(_c4, "BoardingHouseLanding");
__turbopack_context__.k.register(_c5, "PerformanceMonitor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "BailoutToCSR", {
    enumerable: true,
    get: function() {
        return BailoutToCSR;
    }
});
const _bailouttocsr = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js [app-client] (ecmascript)");
function BailoutToCSR(param) {
    let { reason, children } = param;
    if (typeof window === 'undefined') {
        throw Object.defineProperty(new _bailouttocsr.BailoutToCSRError(reason), "__NEXT_ERROR_CODE", {
            value: "E394",
            enumerable: false,
            configurable: true
        });
    }
    return children;
} //# sourceMappingURL=dynamic-bailout-to-csr.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/encode-uri-path.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "encodeURIPath", {
    enumerable: true,
    get: function() {
        return encodeURIPath;
    }
});
function encodeURIPath(file) {
    return file.split('/').map((p)=>encodeURIComponent(p)).join('/');
} //# sourceMappingURL=encode-uri-path.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "PreloadChunks", {
    enumerable: true,
    get: function() {
        return PreloadChunks;
    }
});
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _reactdom = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
const _workasyncstorageexternal = __turbopack_context__.r("[project]/node_modules/next/dist/server/app-render/work-async-storage.external.js [app-client] (ecmascript)");
const _encodeuripath = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/encode-uri-path.js [app-client] (ecmascript)");
function PreloadChunks(param) {
    let { moduleIds } = param;
    // Early return in client compilation and only load requestStore on server side
    if (typeof window !== 'undefined') {
        return null;
    }
    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();
    if (workStore === undefined) {
        return null;
    }
    const allFiles = [];
    // Search the current dynamic call unique key id in react loadable manifest,
    // and find the corresponding CSS files to preload
    if (workStore.reactLoadableManifest && moduleIds) {
        const manifest = workStore.reactLoadableManifest;
        for (const key of moduleIds){
            if (!manifest[key]) continue;
            const chunks = manifest[key].files;
            allFiles.push(...chunks);
        }
    }
    if (allFiles.length === 0) {
        return null;
    }
    const dplId = ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : '';
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {
        children: allFiles.map((chunk)=>{
            const href = workStore.assetPrefix + "/_next/" + (0, _encodeuripath.encodeURIPath)(chunk) + dplId;
            const isCss = chunk.endsWith('.css');
            // If it's stylesheet we use `precedence` o help hoist with React Float.
            // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.
            // The `preload` for stylesheet is not optional.
            if (isCss) {
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("link", {
                    // @ts-ignore
                    precedence: "dynamic",
                    href: href,
                    rel: "stylesheet",
                    as: "style"
                }, chunk);
            } else {
                // If it's script we use ReactDOM.preload to preload the resources
                (0, _reactdom.preload)(href, {
                    as: 'script',
                    fetchPriority: 'low'
                });
                return null;
            }
        })
    });
} //# sourceMappingURL=preload-chunks.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _react = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
const _dynamicbailouttocsr = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js [app-client] (ecmascript)");
const _preloadchunks = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js [app-client] (ecmascript)");
// Normalize loader to return the module as form { default: Component } for `React.lazy`.
// Also for backward compatible since next/dynamic allows to resolve a component directly with loader
// Client component reference proxy need to be converted to a module.
function convertModule(mod) {
    // Check "default" prop before accessing it, as it could be client reference proxy that could break it reference.
    // Cases:
    // mod: { default: Component }
    // mod: Component
    // mod: { default: proxy(Component) }
    // mod: proxy(Component)
    const hasDefault = mod && 'default' in mod;
    return {
        default: hasDefault ? mod.default : mod
    };
}
const defaultOptions = {
    loader: ()=>Promise.resolve(convertModule(()=>null)),
    loading: null,
    ssr: true
};
function Loadable(options) {
    const opts = {
        ...defaultOptions,
        ...options
    };
    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));
    const Loading = opts.loading;
    function LoadableComponent(props) {
        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {
            isLoading: true,
            pastDelay: true,
            error: null
        }) : null;
        // If it's non-SSR or provided a loading component, wrap it in a suspense boundary
        const hasSuspenseBoundary = !opts.ssr || !!opts.loading;
        const Wrap = hasSuspenseBoundary ? _react.Suspense : _react.Fragment;
        const wrapProps = hasSuspenseBoundary ? {
            fallback: fallbackElement
        } : {};
        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {
            children: [
                typeof window === 'undefined' ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadchunks.PreloadChunks, {
                    moduleIds: opts.modules
                }) : null,
                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {
                    ...props
                })
            ]
        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {
            reason: "next/dynamic",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {
                ...props
            })
        });
        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Wrap, {
            ...wrapProps,
            children: children
        });
    }
    LoadableComponent.displayName = 'LoadableComponent';
    return LoadableComponent;
}
const _default = Loadable; //# sourceMappingURL=loadable.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return dynamic;
    }
});
const _interop_require_default = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs [app-client] (ecmascript)");
const _loadable = /*#__PURE__*/ _interop_require_default._(__turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js [app-client] (ecmascript)"));
function dynamic(dynamicOptions, options) {
    var _mergedOptions_loadableGenerated;
    const loadableOptions = {};
    if (typeof dynamicOptions === 'function') {
        loadableOptions.loader = dynamicOptions;
    }
    const mergedOptions = {
        ...loadableOptions,
        ...options
    };
    return (0, _loadable.default)({
        ...mergedOptions,
        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules
    });
}
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=app-dynamic.js.map
}}),
}]);

//# sourceMappingURL=_18803828._.js.map