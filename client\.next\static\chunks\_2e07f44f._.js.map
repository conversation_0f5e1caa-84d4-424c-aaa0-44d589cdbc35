{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/sections/testimonials.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Card } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { ScrollAnimation } from \"@/components/ui/scroll-animation\"\nimport { useTestimonials } from \"@/hooks\"\nimport { getAvatarImage } from \"@/lib/images\"\nimport {\n  Star,\n  Quote,\n  ChevronLeft,\n  ChevronRight,\n  Play,\n  Pause,\n  User\n} from \"lucide-react\"\nimport type { Testimonial } from \"@/types\"\n\n// Mock testimonials data\nconst mockTestimonials: Testimonial[] = [\n  {\n    id: \"1\",\n    name: \"<PERSON>\",\n    avatar: getAvatarImage(0), // Female avatar from Unsplash\n    rating: 5,\n    comment: \"Living here has been an amazing experience! The facilities are top-notch, the community is friendly, and the location is perfect for my daily commute. The 24/7 security gives me peace of mind, and the high-speed WiFi is perfect for my remote work.\",\n    date: \"2024-01-15\",\n    roomType: \"Premium Single\",\n    verified: true\n  },\n  {\n    id: \"2\",\n    name: \"<PERSON>\",\n    avatar: getAvatarImage(1), // Male avatar from Unsplash\n    rating: 5,\n    comment: \"As a university student, this boarding house exceeded all my expectations. The study rooms are quiet and well-lit, the shared kitchen is always clean, and the staff is incredibly helpful. Great value for money!\",\n    date: \"2024-01-10\",\n    roomType: \"Shared Economy\",\n    verified: true\n  },\n  {\n    id: \"3\",\n    name: \"Maria Santos\",\n    avatar: getAvatarImage(2), // Female avatar from Unsplash\n    rating: 5,\n    comment: \"I've been living here for 8 months now and I couldn't be happier. The double room is spacious, the balcony view is beautiful, and I've made so many friends in the common areas. Highly recommend to anyone looking for quality accommodation!\",\n    date: \"2024-01-05\",\n    roomType: \"Premium Double\",\n    verified: true\n  },\n  {\n    id: \"4\",\n    name: \"David Chen\",\n    avatar: getAvatarImage(3), // Male avatar from Unsplash\n    rating: 4,\n    comment: \"Great place for young professionals. The executive suite gives me the privacy I need while still being part of a vibrant community. The fitness corner and entertainment room are nice bonuses. Management is very responsive to any concerns.\",\n    date: \"2023-12-28\",\n    roomType: \"Executive Suite\",\n    verified: true\n  },\n  {\n    id: \"5\",\n    name: \"Lisa Thompson\",\n    avatar: getAvatarImage(4), // Female avatar from Unsplash\n    rating: 5,\n    comment: \"Moving here was the best decision I made when I started my new job in Jakarta. The location is strategic, the amenities are excellent, and the other residents are wonderful. It truly feels like home!\",\n    date: \"2023-12-20\",\n    roomType: \"Deluxe Single\",\n    verified: true\n  }\n]\n\ninterface TestimonialCardProps {\n  testimonial: Testimonial;\n  isActive: boolean;\n}\n\nfunction TestimonialCard({ testimonial, isActive }: TestimonialCardProps) {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <Star\n        key={index}\n        className={`h-4 w-4 ${\n          index < rating \n            ? 'fill-yellow-400 text-yellow-400' \n            : 'text-gray-300'\n        }`}\n      />\n    ))\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ \n        opacity: isActive ? 1 : 0.7,\n        scale: isActive ? 1 : 0.95,\n        y: isActive ? 0 : 10\n      }}\n      transition={{ duration: 0.5, ease: \"easeOut\" }}\n      className=\"w-full\"\n    >\n      <Card className=\"p-8 h-full relative overflow-hidden\">\n        {/* Quote Icon */}\n        <div className=\"absolute top-6 right-6 opacity-10\">\n          <Quote className=\"h-16 w-16 text-blue-600\" />\n        </div>\n\n        <div className=\"relative z-10 space-y-6\">\n          {/* Rating */}\n          <div className=\"flex items-center gap-1\">\n            {renderStars(testimonial.rating)}\n          </div>\n\n          {/* Comment */}\n          <blockquote className=\"text-gray-700 text-lg leading-relaxed italic\">\n            \"{testimonial.comment}\"\n          </blockquote>\n\n          {/* Author Info */}\n          <div className=\"flex items-center gap-4 pt-4 border-t border-gray-100\">\n            <div className=\"relative\">\n              {testimonial.avatar ? (\n                <img\n                  src={testimonial.avatar}\n                  alt={testimonial.name}\n                  className=\"w-12 h-12 rounded-full object-cover\"\n                />\n              ) : (\n                <div className=\"w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center\">\n                  <User className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              )}\n              {testimonial.verified && (\n                <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n              )}\n            </div>\n            \n            <div className=\"flex-1\">\n              <div className=\"flex items-center gap-2\">\n                <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                {testimonial.verified && (\n                  <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                    Verified\n                  </span>\n                )}\n              </div>\n              <div className=\"text-sm text-gray-500 space-y-1\">\n                <p>{testimonial.roomType}</p>\n                <p>{formatDate(testimonial.date)}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </motion.div>\n  )\n}\n\nexport function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = React.useState(0)\n  const [isAutoPlaying, setIsAutoPlaying] = React.useState(true)\n  const [testimonials] = React.useState(mockTestimonials)\n\n  // Auto-play functionality\n  React.useEffect(() => {\n    if (!isAutoPlaying || testimonials.length <= 1) return\n\n    const interval = setInterval(() => {\n      setCurrentIndex(prev => (prev + 1) % testimonials.length)\n    }, 5000)\n\n    return () => clearInterval(interval)\n  }, [isAutoPlaying, testimonials.length])\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(index)\n    setIsAutoPlaying(false)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const nextSlide = () => {\n    setCurrentIndex(prev => (prev + 1) % testimonials.length)\n    setIsAutoPlaying(false)\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const prevSlide = () => {\n    setCurrentIndex(prev => (prev - 1 + testimonials.length) % testimonials.length)\n    setIsAutoPlaying(false)\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const toggleAutoPlay = () => {\n    setIsAutoPlaying(!isAutoPlaying)\n  }\n\n  return (\n    <section id=\"testimonials\" className=\"py-16 bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <ScrollAnimation animation=\"fadeIn\" className=\"text-center mb-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"space-y-4\"\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\n              What Our Residents Say\n            </h2>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              Don't just take our word for it. Here's what our happy residents have to say \n              about their experience living with us.\n            </p>\n          </motion.div>\n        </ScrollAnimation>\n\n        {/* Testimonials Carousel */}\n        <div className=\"relative max-w-4xl mx-auto\">\n          {/* Main Testimonial */}\n          <div className=\"relative min-h-[400px] mb-8\">\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                initial={{ opacity: 0, x: 50 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -50 }}\n                transition={{ duration: 0.5 }}\n                className=\"absolute inset-0\"\n              >\n                <TestimonialCard \n                  testimonial={testimonials[currentIndex]} \n                  isActive={true}\n                />\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Navigation Controls */}\n          <div className=\"flex items-center justify-center gap-4 mb-8\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={prevSlide}\n              className=\"p-2\"\n              aria-label=\"Previous testimonial\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={toggleAutoPlay}\n              className=\"p-2\"\n              aria-label={isAutoPlaying ? \"Pause auto-play\" : \"Resume auto-play\"}\n            >\n              {isAutoPlaying ? (\n                <Pause className=\"h-4 w-4\" />\n              ) : (\n                <Play className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={nextSlide}\n              className=\"p-2\"\n              aria-label=\"Next testimonial\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </Button>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center gap-2 mb-8\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentIndex \n                    ? 'bg-blue-600 scale-125' \n                    : 'bg-gray-300 hover:bg-gray-400'\n                }`}\n                aria-label={`Go to testimonial ${index + 1}`}\n              />\n            ))}\n          </div>\n\n          {/* Stats */}\n          <ScrollAnimation animation=\"slideUp\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">4.9/5</div>\n                <div className=\"text-gray-600\">Average Rating</div>\n                <div className=\"flex justify-center mt-2\">\n                  {Array.from({ length: 5 }, (_, i) => (\n                    <Star key={i} className=\"h-4 w-4 fill-yellow-400 text-yellow-400\" />\n                  ))}\n                </div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"text-center\"\n              >\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">200+</div>\n                <div className=\"text-gray-600\">Happy Residents</div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                className=\"text-center\"\n              >\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">98%</div>\n                <div className=\"text-gray-600\">Satisfaction Rate</div>\n              </motion.div>\n            </div>\n          </ScrollAnimation>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAoBA,yBAAyB;AACzB,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;QACV,UAAU;IACZ;CACD;AAOD,SAAS,gBAAgB,KAA+C;QAA/C,EAAE,WAAW,EAAE,QAAQ,EAAwB,GAA/C;IACvB,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,6LAAC,qMAAA,CAAA,OAAI;gBAEH,WAAW,AAAC,WAIX,OAHC,QAAQ,SACJ,oCACA;eAJD;;;;;IAQX;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YACP,SAAS,WAAW,IAAI;YACxB,OAAO,WAAW,IAAI;YACtB,GAAG,WAAW,IAAI;QACpB;QACA,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAU;kBAEV,cAAA,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;;8BAEd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;8BAGnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,YAAY,YAAY,MAAM;;;;;;sCAIjC,6LAAC;4BAAW,WAAU;;gCAA+C;gCACjE,YAAY,OAAO;gCAAC;;;;;;;sCAIxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,YAAY,MAAM,iBACjB,6LAAC;4CACC,KAAK,YAAY,MAAM;4CACvB,KAAK,YAAY,IAAI;4CACrB,WAAU;;;;;iEAGZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;wCAGnB,YAAY,QAAQ,kBACnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAe,SAAQ;0DAC9D,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;8CAMjK,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA+B,YAAY,IAAI;;;;;;gDAC5D,YAAY,QAAQ,kBACnB,6LAAC;oDAAK,WAAU;8DAA6D;;;;;;;;;;;;sDAKjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAG,YAAY,QAAQ;;;;;;8DACxB,6LAAC;8DAAG,WAAW,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KA5FS;AA8FF,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,WAAc,CAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,6JAAA,CAAA,WAAc,CAAC;IACzD,MAAM,CAAC,aAAa,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEtC,0BAA0B;IAC1B,6JAAA,CAAA,YAAe;yCAAC;YACd,IAAI,CAAC,iBAAiB,aAAa,MAAM,IAAI,GAAG;YAEhD,MAAM,WAAW;0DAAY;oBAC3B;kEAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;gBAC1D;yDAAG;YAEH;iDAAO,IAAM,cAAc;;QAC7B;wCAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QACxD,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAA,OAAQ,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;QAC9E,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,iBAAiB;QACrB,iBAAiB,CAAC;IACpB;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,2IAAA,CAAA,kBAAe;oBAAC,WAAU;oBAAS,WAAU;8BAC5C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;8BAQ3D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,6LAAC;wCACC,aAAa,YAAY,CAAC,aAAa;wCACvC,UAAU;;;;;;mCATP;;;;;;;;;;;;;;;sCAgBX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAGzB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAY,gBAAgB,oBAAoB;8CAE/C,8BACC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAEjB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAIpB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK5B,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,AAAC,oDAIX,OAHC,UAAU,eACN,0BACA;oCAEN,cAAY,AAAC,qBAA8B,OAAV,QAAQ;mCAPpC;;;;;;;;;;sCAaX,6LAAC,2IAAA,CAAA,kBAAe;4BAAC,WAAU;sCACzB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ;gDAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC,qMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;;;;;;;kDAKjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAGjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GAjLgB;MAAA", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/quote.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/quote.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z',\n      key: 'rib7q0',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z',\n      key: '1ymkrd',\n    },\n  ],\n];\n\n/**\n * @component @name Quote\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgM2EyIDIgMCAwIDAtMiAydjZhMiAyIDAgMCAwIDIgMiAxIDEgMCAwIDEgMSAxdjFhMiAyIDAgMCAxLTIgMiAxIDEgMCAwIDAtMSAxdjJhMSAxIDAgMCAwIDEgMSA2IDYgMCAwIDAgNi02VjVhMiAyIDAgMCAwLTItMnoiIC8+CiAgPHBhdGggZD0iTTUgM2EyIDIgMCAwIDAtMiAydjZhMiAyIDAgMCAwIDIgMiAxIDEgMCAwIDEgMSAxdjFhMiAyIDAgMCAxLTIgMiAxIDEgMCAwIDAtMSAxdjJhMSAxIDAgMCAwIDEgMSA2IDYgMCAwIDAgNi02VjVhMiAyIDAgMCAwLTItMnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/quote\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Quote = createLucideIcon('quote', __iconNode);\n\nexport default Quote;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/chevron-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm15 18-6-6 6-6', key: '1wnfg3' }]];\n\n/**\n * @component @name ChevronLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtNi02IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronLeft = createLucideIcon('chevron-left', __iconNode);\n\nexport default ChevronLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,gBAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,eAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/play.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,SAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,oBAAsB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/pause.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/pause.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { x: '14', y: '4', width: '4', height: '16', rx: '1', key: 'zuxfzm' }],\n  ['rect', { x: '6', y: '4', width: '4', height: '16', rx: '1', key: '1okwgv' }],\n];\n\n/**\n * @component @name Pause\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNCIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iMTYiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjYiIHk9IjQiIHdpZHRoPSI0IiBoZWlnaHQ9IjE2IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pause\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pause = createLucideIcon('pause', __iconNode);\n\nexport default Pause;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,EAAK;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC/E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/user.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}