(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_cea3c7b2._.js",
  "static/chunks/components_sections_hero_tsx_dee112bf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_9920074e._.js",
  "static/chunks/node_modules_lucide-react_dist_esm_icons_fb8c4428._.js",
  "static/chunks/components_sections_rooms_tsx_dee112bf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_a52c4a65._.js",
  "static/chunks/components_sections_facilities_tsx_dee112bf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_61ad9bc5._.js",
  "static/chunks/components_sections_testimonials_tsx_dee112bf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);