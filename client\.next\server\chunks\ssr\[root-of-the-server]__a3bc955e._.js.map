{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/navigation.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useEffect } from \"react\"\r\nimport Link from \"next/link\"\r\nimport { motion, AnimatePresence } from \"framer-motion\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\"\r\nimport {\r\n  Home,\r\n  Building,\r\n  Star,\r\n  User,\r\n  Menu,\r\n  Building2,\r\n  Phone,\r\n  X\r\n} from \"lucide-react\"\r\nimport { fadeInVariants, mobileMenuVariants, menuItemVariants } from \"@/utils/animations\"\r\n\r\nconst navigation = [\r\n  { name: \"Home\", href: \"/\", icon: Home },\r\n  { name: \"Facilities\", href: \"#facilities\", icon: Building },\r\n  { name: \"Testimonials\", href: \"#testimonials\", icon: Star },\r\n  { name: \"Contact\", href: \"#contact\", icon: Phone },\r\n]\r\n\r\nexport function Navigation() {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [scrolled, setScrolled] = useState(false)\r\n\r\n  // Handle scroll effect\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setScrolled(window.scrollY > 10)\r\n    }\r\n    window.addEventListener('scroll', handleScroll)\r\n    return () => window.removeEventListener('scroll', handleScroll)\r\n  }, [])\r\n\r\n  // Handle smooth scroll to sections\r\n  const handleNavClick = (href: string) => {\r\n    if (href.startsWith('#')) {\r\n      const element = document.querySelector(href)\r\n      if (element) {\r\n        element.scrollIntoView({ behavior: 'smooth' })\r\n      }\r\n    }\r\n    setIsOpen(false)\r\n  }\r\n\r\n  return (\r\n    <motion.header\r\n      className={`sticky top-0 z-50 w-full border-b transition-all duration-300 ${\r\n        scrolled\r\n          ? 'bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/80 shadow-sm'\r\n          : 'bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'\r\n      }`}\r\n      variants={fadeInVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n    >\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"flex h-16 items-center justify-between\">\r\n          {/* Logo */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.1 }}\r\n          >\r\n            <Link href=\"/\" className=\"flex items-center space-x-2 group\">\r\n              <Building2 className=\"h-8 w-8 text-blue-600 group-hover:text-blue-700 transition-colors\" />\r\n              <span className=\"text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors\">\r\n                BoardingHouse\r\n              </span>\r\n            </Link>\r\n          </motion.div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <motion.nav\r\n            className=\"hidden md:flex items-center space-x-8\"\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n          >\r\n            {navigation.map((item, index) => (\r\n              <motion.div\r\n                key={item.name}\r\n                initial={{ opacity: 0, y: -10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}\r\n              >\r\n                <Link\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href)}\r\n                  className=\"flex items-center space-x-1 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors relative group\"\r\n                >\r\n                  <item.icon className=\"h-4 w-4\" />\r\n                  <span>{item.name}</span>\r\n                  <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n              </motion.div>\r\n            ))}\r\n          </motion.nav>\r\n\r\n          {/* Desktop Actions */}\r\n          <motion.div\r\n            className=\"hidden md:flex items-center space-x-4\"\r\n            initial={{ opacity: 0, x: 20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.4 }}\r\n          >\r\n            <Button variant=\"outline\" size=\"sm\" className=\"border-blue-600 text-blue-600 hover:bg-blue-50\">\r\n              View Rooms\r\n            </Button>\r\n            <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\r\n              Contact Us\r\n            </Button>\r\n          </motion.div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"md:hidden\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => setIsOpen(true)}\r\n              aria-label=\"Open navigation menu\"\r\n            >\r\n              <Menu className=\"h-5 w-5\" />\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Mobile Menu Overlay */}\r\n          <AnimatePresence>\r\n            {isOpen && (\r\n              <>\r\n                {/* Backdrop */}\r\n                <motion.div\r\n                  className=\"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm md:hidden\"\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  exit={{ opacity: 0 }}\r\n                  onClick={() => setIsOpen(false)}\r\n                />\r\n\r\n                {/* Mobile Menu */}\r\n                <motion.div\r\n                  className=\"fixed top-0 right-0 z-50 h-full w-80 bg-background shadow-xl md:hidden\"\r\n                  variants={mobileMenuVariants}\r\n                  initial=\"closed\"\r\n                  animate=\"open\"\r\n                  exit=\"closed\"\r\n                >\r\n                  <div className=\"flex flex-col h-full\">\r\n                    {/* Header */}\r\n                    <div className=\"flex items-center justify-between p-6 border-b\">\r\n                      <span className=\"text-lg font-semibold\">Menu</span>\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        onClick={() => setIsOpen(false)}\r\n                        aria-label=\"Close navigation menu\"\r\n                      >\r\n                        <X className=\"h-5 w-5\" />\r\n                      </Button>\r\n                    </div>\r\n\r\n                    {/* Navigation Items */}\r\n                    <div className=\"flex-1 p-6\">\r\n                      <nav className=\"space-y-4\">\r\n                        {navigation.map((item, index) => (\r\n                          <motion.div\r\n                            key={item.name}\r\n                            variants={menuItemVariants}\r\n                            initial=\"closed\"\r\n                            animate=\"open\"\r\n                            custom={index}\r\n                          >\r\n                            <Link\r\n                              href={item.href}\r\n                              onClick={() => handleNavClick(item.href)}\r\n                              className=\"flex items-center space-x-3 text-lg font-medium text-gray-600 hover:text-blue-600 transition-colors p-3 rounded-lg hover:bg-blue-50\"\r\n                            >\r\n                              <item.icon className=\"h-5 w-5\" />\r\n                              <span>{item.name}</span>\r\n                            </Link>\r\n                          </motion.div>\r\n                        ))}\r\n                      </nav>\r\n\r\n                      {/* Mobile Actions */}\r\n                      <div className=\"pt-6 mt-6 border-t space-y-3\">\r\n                        <Button variant=\"outline\" className=\"w-full justify-start border-blue-600 text-blue-600 hover:bg-blue-50\">\r\n                          View Rooms\r\n                        </Button>\r\n                        <Button className=\"w-full justify-start bg-blue-600 hover:bg-blue-700\">\r\n                          Contact Us\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n      </div>\r\n    </motion.header>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC1D;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC1D;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;IAAC;CAClD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF;QACA,UAAU;IACZ;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAC,8DAA8D,EACxE,WACI,yFACA,8EACJ;QACF,UAAU,mHAAA,CAAA,iBAAc;QACxB,SAAQ;QACR,SAAQ;kBAER,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;;;;;;kCAOnG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAEvC,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;0CAEtD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,eAAe,KAAK,IAAI;oCACvC,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;sDAChB,8OAAC;4CAAK,WAAU;;;;;;;;;;;;+BAZb,KAAK,IAAI;;;;;;;;;;kCAmBpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAiD;;;;;;0CAG/F,8OAAC,2HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;kCAM9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,UAAU;4BACzB,cAAW;sCAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAKpB,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC;;8CAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,MAAM;wCAAE,SAAS;oCAAE;oCACnB,SAAS,IAAM,UAAU;;;;;;8CAI3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,UAAU,mHAAA,CAAA,qBAAkB;oCAC5B,SAAQ;oCACR,SAAQ;oCACR,MAAK;8CAEL,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,2HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,cAAW;kEAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,UAAU,mHAAA,CAAA,mBAAgB;gEAC1B,SAAQ;gEACR,SAAQ;gEACR,QAAQ;0EAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,SAAS,IAAM,eAAe,KAAK,IAAI;oEACvC,WAAU;;sFAEV,8OAAC,KAAK,IAAI;4EAAC,WAAU;;;;;;sFACrB,8OAAC;sFAAM,KAAK,IAAI;;;;;;;;;;;;+DAZb,KAAK,IAAI;;;;;;;;;;kEAmBpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2HAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,WAAU;0EAAsE;;;;;;0EAG1G,8OAAC,2HAAA,CAAA,SAAM;gEAAC,WAAU;0EAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc/F", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/ui/scroll-animation.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion, Variants } from \"framer-motion\"\nimport { useInView } from \"react-intersection-observer\"\n\nimport { cn } from \"@/lib/utils\"\nimport { getAnimationVariant, intersectionObserverOptions } from \"@/utils/animations\"\nimport type { ScrollAnimationProps } from \"@/types\"\n\nexport function ScrollAnimation({\n  children,\n  animation = \"fadeIn\",\n  duration = 0.6,\n  delay = 0,\n  threshold = 0.1,\n  triggerOnce = true,\n  className,\n}: ScrollAnimationProps) {\n  const { ref, inView } = useInView({\n    threshold,\n    triggerOnce,\n    rootMargin: intersectionObserverOptions.rootMargin,\n  });\n\n  const variants = getAnimationVariant(animation);\n\n  return (\n    <motion.div\n      ref={ref}\n      className={cn(className)}\n      variants={variants}\n      initial=\"hidden\"\n      animate={inView ? \"visible\" : \"hidden\"}\n      transition={{\n        duration,\n        delay,\n        ease: \"easeOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\n// Staggered animation container for multiple children\nexport function StaggeredAnimation({\n  children,\n  staggerDelay = 0.1,\n  className,\n}: {\n  children: React.ReactNode;\n  staggerDelay?: number;\n  className?: string;\n}) {\n  const { ref, inView } = useInView({\n    threshold: 0.1,\n    triggerOnce: true,\n  });\n\n  const containerVariants: Variants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: staggerDelay,\n        delayChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants: Variants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      ref={ref}\n      className={cn(className)}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate={inView ? \"visible\" : \"hidden\"}\n    >\n      {React.Children.map(children, (child, index) => (\n        <motion.div key={index} variants={itemVariants}>\n          {child}\n        </motion.div>\n      ))}\n    </motion.div>\n  );\n}\n\n// Parallax scroll effect component\nexport function ParallaxScroll({\n  children,\n  offset = 50,\n  className,\n}: {\n  children: React.ReactNode;\n  offset?: number;\n  className?: string;\n}) {\n  const [scrollY, setScrollY] = React.useState(0);\n  const ref = React.useRef<HTMLDivElement>(null);\n\n  React.useEffect(() => {\n    const handleScroll = () => {\n      if (ref.current) {\n        const rect = ref.current.getBoundingClientRect();\n        const scrolled = window.scrollY;\n        const rate = scrolled * -0.5;\n        setScrollY(rate);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <div ref={ref} className={cn(\"relative\", className)}>\n      <motion.div\n        style={{\n          y: scrollY,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 300,\n          damping: 30,\n        }}\n      >\n        {children}\n      </motion.div>\n    </div>\n  );\n}\n\n// Fade in on scroll with custom trigger point\nexport function FadeInOnScroll({\n  children,\n  delay = 0,\n  duration = 0.6,\n  triggerPoint = 0.1,\n  className,\n}: {\n  children: React.ReactNode;\n  delay?: number;\n  duration?: number;\n  triggerPoint?: number;\n  className?: string;\n}) {\n  const { ref, inView } = useInView({\n    threshold: triggerPoint,\n    triggerOnce: true,\n  });\n\n  return (\n    <motion.div\n      ref={ref}\n      className={cn(className)}\n      initial={{ opacity: 0, y: 30 }}\n      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n      transition={{\n        duration,\n        delay,\n        ease: \"easeOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\n// Slide in from direction\nexport function SlideInOnScroll({\n  children,\n  direction = \"up\",\n  distance = 50,\n  delay = 0,\n  duration = 0.6,\n  className,\n}: {\n  children: React.ReactNode;\n  direction?: \"up\" | \"down\" | \"left\" | \"right\";\n  distance?: number;\n  delay?: number;\n  duration?: number;\n  className?: string;\n}) {\n  const { ref, inView } = useInView({\n    threshold: 0.1,\n    triggerOnce: true,\n  });\n\n  const getInitialPosition = () => {\n    switch (direction) {\n      case \"up\":\n        return { x: 0, y: distance };\n      case \"down\":\n        return { x: 0, y: -distance };\n      case \"left\":\n        return { x: distance, y: 0 };\n      case \"right\":\n        return { x: -distance, y: 0 };\n      default:\n        return { x: 0, y: distance };\n    }\n  };\n\n  const initial = {\n    opacity: 0,\n    ...getInitialPosition(),\n  };\n\n  const animate = inView\n    ? { opacity: 1, x: 0, y: 0 }\n    : initial;\n\n  return (\n    <motion.div\n      ref={ref}\n      className={cn(className)}\n      initial={initial}\n      animate={animate}\n      transition={{\n        duration,\n        delay,\n        ease: \"easeOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\n// Scale in animation\nexport function ScaleInOnScroll({\n  children,\n  delay = 0,\n  duration = 0.5,\n  initialScale = 0.8,\n  className,\n}: {\n  children: React.ReactNode;\n  delay?: number;\n  duration?: number;\n  initialScale?: number;\n  className?: string;\n}) {\n  const { ref, inView } = useInView({\n    threshold: 0.1,\n    triggerOnce: true,\n  });\n\n  return (\n    <motion.div\n      ref={ref}\n      className={cn(className)}\n      initial={{ opacity: 0, scale: initialScale }}\n      animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: initialScale }}\n      transition={{\n        duration,\n        delay,\n        ease: \"easeOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\n// Counter animation for numbers\nexport function CounterAnimation({\n  from = 0,\n  to,\n  duration = 2,\n  className,\n}: {\n  from?: number;\n  to: number;\n  duration?: number;\n  className?: string;\n}) {\n  const { ref, inView } = useInView({\n    threshold: 0.5,\n    triggerOnce: true,\n  });\n\n  const [count, setCount] = React.useState(from);\n\n  React.useEffect(() => {\n    if (inView) {\n      const startTime = Date.now();\n      const endTime = startTime + duration * 1000;\n\n      const updateCount = () => {\n        const now = Date.now();\n        const progress = Math.min((now - startTime) / (endTime - startTime), 1);\n        const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n        const currentCount = Math.floor(from + (to - from) * easeOutQuart);\n        \n        setCount(currentCount);\n\n        if (progress < 1) {\n          requestAnimationFrame(updateCount);\n        }\n      };\n\n      requestAnimationFrame(updateCount);\n    }\n  }, [inView, from, to, duration]);\n\n  return (\n    <span ref={ref} className={cn(className)}>\n      {count.toLocaleString()}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAUO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,QAAQ,EACpB,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,YAAY,GAAG,EACf,cAAc,IAAI,EAClB,SAAS,EACY;IACrB,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAChC;QACA;QACA,YAAY,mHAAA,CAAA,8BAA2B,CAAC,UAAU;IACpD;IAEA,MAAM,WAAW,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;IAErC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;QACd,UAAU;QACV,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;AAGO,SAAS,mBAAmB,EACjC,QAAQ,EACR,eAAe,GAAG,EAClB,SAAS,EAKV;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAChC,WAAW;QACX,aAAa;IACf;IAEA,MAAM,oBAA8B;QAClC,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAyB;QAC7B,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;QACd,UAAU;QACV,SAAQ;QACR,SAAS,SAAS,YAAY;kBAE7B,qMAAA,CAAA,WAAc,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,sBACpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAa,UAAU;0BAC/B;eADc;;;;;;;;;;AAMzB;AAGO,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EAAE,EACX,SAAS,EAKV;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,WAAc,CAAC;IAC7C,MAAM,MAAM,qMAAA,CAAA,SAAY,CAAiB;IAEzC,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,eAAe;YACnB,IAAI,IAAI,OAAO,EAAE;gBACf,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB;gBAC9C,MAAM,WAAW,OAAO,OAAO;gBAC/B,MAAM,OAAO,WAAW,CAAC;gBACzB,WAAW;YACb;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBACvC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,OAAO;gBACL,GAAG;YACL;YACA,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;sBAEC;;;;;;;;;;;AAIT;AAGO,SAAS,eAAe,EAC7B,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,eAAe,GAAG,EAClB,SAAS,EAOV;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAChC,WAAW;QACX,aAAa;IACf;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7D,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;AAGO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,SAAS,EAQV;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAChC,WAAW;QACX,aAAa;IACf;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,GAAG;oBAAG,GAAG;gBAAS;YAC7B,KAAK;gBACH,OAAO;oBAAE,GAAG;oBAAG,GAAG,CAAC;gBAAS;YAC9B,KAAK;gBACH,OAAO;oBAAE,GAAG;oBAAU,GAAG;gBAAE;YAC7B,KAAK;gBACH,OAAO;oBAAE,GAAG,CAAC;oBAAU,GAAG;gBAAE;YAC9B;gBACE,OAAO;oBAAE,GAAG;oBAAG,GAAG;gBAAS;QAC/B;IACF;IAEA,MAAM,UAAU;QACd,SAAS;QACT,GAAG,oBAAoB;IACzB;IAEA,MAAM,UAAU,SACZ;QAAE,SAAS;QAAG,GAAG;QAAG,GAAG;IAAE,IACzB;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,SAAS;QACT,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;AAGO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,eAAe,GAAG,EAClB,SAAS,EAOV;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAChC,WAAW;QACX,aAAa;IACf;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;YAAE,SAAS;YAAG,OAAO;QAAa;QAC3C,SAAS,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE,IAAI;YAAE,SAAS;YAAG,OAAO;QAAa;QAC/E,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;AAGO,SAAS,iBAAiB,EAC/B,OAAO,CAAC,EACR,EAAE,EACF,WAAW,CAAC,EACZ,SAAS,EAMV;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAChC,WAAW;QACX,aAAa;IACf;IAEA,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEzC,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,QAAQ;YACV,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,UAAU,YAAY,WAAW;YAEvC,MAAM,cAAc;gBAClB,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,MAAM,SAAS,IAAI,CAAC,UAAU,SAAS,GAAG;gBACrE,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;gBAChD,MAAM,eAAe,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI;gBAErD,SAAS;gBAET,IAAI,WAAW,GAAG;oBAChB,sBAAsB;gBACxB;YACF;YAEA,sBAAsB;QACxB;IACF,GAAG;QAAC;QAAQ;QAAM;QAAI;KAAS;IAE/B,qBACE,8OAAC;QAAK,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;kBAC3B,MAAM,cAAc;;;;;;AAG3B", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { ScrollAnimation, FadeInOnScroll } from \"@/components/ui/scroll-animation\"\nimport {\n  Building2,\n  Mail,\n  Phone,\n  MapPin,\n  Facebook,\n  Twitter,\n  Instagram,\n  Youtube,\n  Clock,\n  Shield,\n  Wifi\n} from \"lucide-react\"\n\nconst footerLinks = {\n  quickLinks: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"Rooms\", href: \"#rooms\" },\n    { name: \"Facilities\", href: \"#facilities\" },\n    { name: \"Testimonials\", href: \"#testimonials\" },\n  ],\n  services: [\n    { name: \"Room Booking\", href: \"#contact\" },\n    { name: \"Virtual Tour\", href: \"#rooms\" },\n    { name: \"Monthly Rates\", href: \"#contact\" },\n    { name: \"Student Discount\", href: \"#contact\" },\n  ],\n  support: [\n    { name: \"Contact Us\", href: \"#contact\" },\n    { name: \"FAQ\", href: \"#faq\" },\n    { name: \"House Rules\", href: \"#rules\" },\n    { name: \"Payment Info\", href: \"#payment\" },\n  ],\n  legal: [\n    { name: \"Terms & Conditions\", href: \"/terms\" },\n    { name: \"Privacy Policy\", href: \"/privacy\" },\n    { name: \"Booking Policy\", href: \"/booking-policy\" },\n    { name: \"Cancellation\", href: \"/cancellation\" },\n  ]\n}\n\nconst socialLinks = [\n  { name: \"Facebook\", icon: Facebook, href: \"https://facebook.com/boardinghouse\" },\n  { name: \"Twitter\", icon: Twitter, href: \"https://twitter.com/boardinghouse\" },\n  { name: \"Instagram\", icon: Instagram, href: \"https://instagram.com/boardinghouse\" },\n  { name: \"YouTube\", icon: Youtube, href: \"https://youtube.com/boardinghouse\" },\n]\n\nconst contactInfo = {\n  email: \"<EMAIL>\",\n  phone: \"+62 21 1234 5678\",\n  whatsapp: \"+62 812 3456 7890\",\n  address: \"Jl. Sudirman No. 123, Jakarta Pusat, Indonesia 10220\"\n}\n\nexport function Footer() {\n  return (\n    <footer id=\"contact\" className=\"bg-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <ScrollAnimation animation=\"fadeIn\" className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <FadeInOnScroll delay={0.1} className=\"lg:col-span-1 space-y-6\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Building2 className=\"h-8 w-8 text-blue-400\" />\n              <span className=\"text-2xl font-bold text-white\">BoardingHouse</span>\n            </Link>\n            <p className=\"text-gray-300 leading-relaxed\">\n              Your ideal boarding house with modern facilities, comfortable rooms, and a safe environment.\n              Perfect for students and professionals.\n            </p>\n\n            {/* Key Features */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-3 text-sm text-gray-300\">\n                <Clock className=\"h-4 w-4 text-blue-400\" />\n                <span>24/7 Security & Support</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-sm text-gray-300\">\n                <Wifi className=\"h-4 w-4 text-blue-400\" />\n                <span>High-Speed WiFi</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-sm text-gray-300\">\n                <Shield className=\"h-4 w-4 text-blue-400\" />\n                <span>Safe & Clean Environment</span>\n              </div>\n            </div>\n          </FadeInOnScroll>\n\n          {/* Contact Info */}\n          <FadeInOnScroll delay={0.2} className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-white\">Contact Info</h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start gap-3 text-sm text-gray-300\">\n                <Mail className=\"h-4 w-4 text-blue-400 mt-0.5\" />\n                <div>\n                  <p className=\"font-medium text-white\">Email</p>\n                  <a href={`mailto:${contactInfo.email}`} className=\"hover:text-blue-400 transition-colors\">\n                    {contactInfo.email}\n                  </a>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3 text-sm text-gray-300\">\n                <Phone className=\"h-4 w-4 text-blue-400 mt-0.5\" />\n                <div>\n                  <p className=\"font-medium text-white\">Phone</p>\n                  <a href={`tel:${contactInfo.phone}`} className=\"hover:text-blue-400 transition-colors\">\n                    {contactInfo.phone}\n                  </a>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3 text-sm text-gray-300\">\n                <MapPin className=\"h-4 w-4 text-blue-400 mt-0.5\" />\n                <div>\n                  <p className=\"font-medium text-white\">Address</p>\n                  <p className=\"leading-relaxed\">{contactInfo.address}</p>\n                </div>\n              </div>\n            </div>\n          </FadeInOnScroll>\n\n          {/* Quick Links */}\n          <FadeInOnScroll delay={0.3} className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-white\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.quickLinks.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-gray-300 hover:text-blue-400 transition-colors flex items-center group\"\n                  >\n                    <span className=\"group-hover:translate-x-1 transition-transform\">\n                      {link.name}\n                    </span>\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n            <div className=\"pt-4\">\n              <h4 className=\"font-semibold text-white mb-3\">Services</h4>\n              <ul className=\"space-y-2\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-gray-300 hover:text-blue-400 transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </FadeInOnScroll>\n\n          {/* Support & Legal */}\n          <FadeInOnScroll delay={0.4} className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-white\">Support</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-gray-300 hover:text-blue-400 transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n            <div className=\"pt-4\">\n              <h4 className=\"font-semibold text-white mb-3\">Legal</h4>\n              <ul className=\"space-y-2\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-xs text-gray-400 hover:text-blue-400 transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"pt-4\">\n              <h4 className=\"font-semibold text-white mb-3\">Follow Us</h4>\n              <div className=\"flex space-x-3\">\n                {socialLinks.map((social) => (\n                  <motion.div\n                    key={social.name}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"h-9 w-9 p-0 bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white transition-all duration-300\"\n                      asChild\n                    >\n                      <Link href={social.href} aria-label={social.name} target=\"_blank\" rel=\"noopener noreferrer\">\n                        <social.icon className=\"h-4 w-4\" />\n                      </Link>\n                    </Button>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </FadeInOnScroll>\n        </ScrollAnimation>\n\n        <Separator className=\"my-8 bg-gray-700\" />\n\n        {/* Bottom Section */}\n        <FadeInOnScroll delay={0.5}>\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            <p className=\"text-sm text-gray-400\">\n              © 2024 BoardingHouse. All rights reserved.\n            </p>\n\n            <div className=\"flex items-center gap-6 text-sm text-gray-400\">\n              <span>Made with ❤️ for comfortable living</span>\n              <div className=\"flex items-center gap-2\">\n                <span>Available 24/7</span>\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              </div>\n            </div>\n          </div>\n        </FadeInOnScroll>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAqBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;IACD,UAAU;QACR;YAAE,MAAM;YAAgB,MAAM;QAAW;QACzC;YAAE,MAAM;YAAgB,MAAM;QAAS;QACvC;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAoB,MAAM;QAAW;KAC9C;IACD,SAAS;QACP;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAO,MAAM;QAAO;QAC5B;YAAE,MAAM;YAAe,MAAM;QAAS;QACtC;YAAE,MAAM;YAAgB,MAAM;QAAW;KAC1C;IACD,OAAO;QACL;YAAE,MAAM;YAAsB,MAAM;QAAS;QAC7C;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAkB,MAAM;QAAkB;QAClD;YAAE,MAAM;YAAgB,MAAM;QAAgB;KAC/C;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;IAAqC;IAC/E;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;IAAoC;IAC5E;QAAE,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;IAAsC;IAClF;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;IAAoC;CAC7E;AAED,MAAM,cAAc;IAClB,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;AACX;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,IAAG;QAAU,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wIAAA,CAAA,kBAAe;oBAAC,WAAU;oBAAS,WAAU;;sCAE5C,8OAAC,wIAAA,CAAA,iBAAc;4BAAC,OAAO;4BAAK,WAAU;;8CACpC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;8CAElD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAM7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC,wIAAA,CAAA,iBAAc;4BAAC,OAAO;4BAAK,WAAU;;8CACpC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;sEACtC,8OAAC;4DAAE,MAAM,CAAC,OAAO,EAAE,YAAY,KAAK,EAAE;4DAAE,WAAU;sEAC/C,YAAY,KAAK;;;;;;;;;;;;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;sEACtC,8OAAC;4DAAE,MAAM,CAAC,IAAI,EAAE,YAAY,KAAK,EAAE;4DAAE,WAAU;sEAC5C,YAAY,KAAK;;;;;;;;;;;;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAmB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO3D,8OAAC,wIAAA,CAAA,iBAAc;4BAAC,OAAO;4BAAK,WAAU;;8CACpC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;8CACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DACb,KAAK,IAAI;;;;;;;;;;;2CANP,KAAK,IAAI;;;;;;;;;;8CAatB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAG,WAAU;sDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAc1B,8OAAC,wIAAA,CAAA,iBAAc;4BAAC,OAAO;4BAAK,WAAU;;8CACpC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;8CAWtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,OAAO;kEAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,OAAO,IAAI;4DAAE,cAAY,OAAO,IAAI;4DAAE,QAAO;4DAAS,KAAI;sEACpE,cAAA,8OAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;mDAXtB,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAqB5B,8OAAC,8HAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BAGrB,8OAAC,wIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAIrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B", "debugId": null}}, {"offset": {"line": 1524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/ui/loading.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { spinnerVariants } from \"@/utils/animations\"\n\nconst loadingVariants = cva(\n  \"flex items-center justify-center\",\n  {\n    variants: {\n      variant: {\n        spinner: \"\",\n        skeleton: \"animate-pulse bg-muted rounded\",\n        dots: \"space-x-1\",\n      },\n      size: {\n        sm: \"h-4 w-4\",\n        md: \"h-6 w-6\",\n        lg: \"h-8 w-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"spinner\",\n      size: \"md\",\n    },\n  }\n)\n\ninterface LoadingProps extends VariantProps<typeof loadingVariants> {\n  text?: string;\n  className?: string;\n}\n\n// Spinner Loading Component\nfunction SpinnerLoading({ size, className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  const sizeClasses = {\n    sm: \"h-4 w-4 border-2\",\n    md: \"h-6 w-6 border-2\",\n    lg: \"h-8 w-8 border-3\",\n  };\n\n  return (\n    <motion.div\n      className={cn(\n        \"rounded-full border-current border-t-transparent\",\n        sizeClasses[size || \"md\"],\n        className\n      )}\n      variants={spinnerVariants}\n      animate=\"animate\"\n      aria-hidden=\"true\"\n    />\n  );\n}\n\n// Skeleton Loading Component\nfunction SkeletonLoading({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  );\n}\n\n// Dots Loading Component\nfunction DotsLoading({ size, className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  const dotSizes = {\n    sm: \"h-1 w-1\",\n    md: \"h-2 w-2\",\n    lg: \"h-3 w-3\",\n  };\n\n  const dotSize = dotSizes[size || \"md\"];\n\n  return (\n    <div className={cn(\"flex space-x-1\", className)}>\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className={cn(\"bg-current rounded-full\", dotSize)}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 0.6,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n          aria-hidden=\"true\"\n        />\n      ))}\n    </div>\n  );\n}\n\n// Main Loading Component\nexport function Loading({ variant = \"spinner\", size = \"md\", text, className }: LoadingProps) {\n  const renderLoading = () => {\n    switch (variant) {\n      case \"skeleton\":\n        return <SkeletonLoading className={cn(loadingVariants({ size }), className)} />;\n      case \"dots\":\n        return <DotsLoading size={size || undefined} className={className} />;\n      case \"spinner\":\n      default:\n        return <SpinnerLoading size={size || undefined} className={className} />;\n    }\n  };\n\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center gap-2\", className)}>\n      {renderLoading()}\n      {text && (\n        <p className=\"text-sm text-muted-foreground animate-pulse\" aria-live=\"polite\">\n          {text}\n        </p>\n      )}\n    </div>\n  );\n}\n\n// Individual loading components for specific use cases\nexport function LoadingSpinner({ size = \"md\", className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  return <SpinnerLoading size={size} className={className} />;\n}\n\nexport function LoadingSkeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return <SkeletonLoading className={className} {...props} />;\n}\n\nexport function LoadingDots({ size = \"md\", className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  return <DotsLoading size={size} className={className} />;\n}\n\n// Skeleton variants for different content types\nexport function SkeletonText({ lines = 3, className }: { lines?: number; className?: string }) {\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      {Array.from({ length: lines }).map((_, index) => (\n        <SkeletonLoading\n          key={index}\n          className={cn(\n            \"h-4\",\n            index === lines - 1 ? \"w-3/4\" : \"w-full\" // Last line is shorter\n          )}\n        />\n      ))}\n    </div>\n  );\n}\n\nexport function SkeletonCard({ className }: { className?: string }) {\n  return (\n    <div className={cn(\"space-y-4 p-4\", className)}>\n      <SkeletonLoading className=\"h-48 w-full\" />\n      <div className=\"space-y-2\">\n        <SkeletonLoading className=\"h-4 w-3/4\" />\n        <SkeletonLoading className=\"h-4 w-1/2\" />\n      </div>\n    </div>\n  );\n}\n\nexport function SkeletonAvatar({ size = \"md\", className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  const sizeClasses = {\n    sm: \"h-8 w-8\",\n    md: \"h-10 w-10\",\n    lg: \"h-12 w-12\",\n  };\n\n  return (\n    <SkeletonLoading\n      className={cn(\"rounded-full\", sizeClasses[size], className)}\n    />\n  );\n}\n\n// Loading overlay for full-screen loading states\nexport function LoadingOverlay({ \n  isVisible, \n  text = \"Loading...\", \n  className \n}: { \n  isVisible: boolean; \n  text?: string; \n  className?: string; \n}) {\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      className={cn(\n        \"fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm\",\n        className\n      )}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.2 }}\n    >\n      <div className=\"flex flex-col items-center space-y-4\">\n        <LoadingSpinner size=\"lg\" />\n        <p className=\"text-lg font-medium\" aria-live=\"polite\">\n          {text}\n        </p>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AASA,MAAM,kBAAkB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACxB,oCACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAQF,4BAA4B;AAC5B,SAAS,eAAe,EAAE,IAAI,EAAE,SAAS,EAAqD;IAC5F,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oDACA,WAAW,CAAC,QAAQ,KAAK,EACzB;QAEF,UAAU,mHAAA,CAAA,kBAAe;QACzB,SAAQ;QACR,eAAY;;;;;;AAGlB;AAEA,6BAA6B;AAC7B,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAA6C;IACpF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;AAEA,yBAAyB;AACzB,SAAS,YAAY,EAAE,IAAI,EAAE,SAAS,EAAqD;IACzF,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,QAAQ,CAAC,QAAQ,KAAK;IAEtC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;gBACzC,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;gBACA,eAAY;eAXP;;;;;;;;;;AAgBf;AAGO,SAAS,QAAQ,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,IAAI,EAAE,SAAS,EAAgB;IACzF,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAgB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;wBAAE;oBAAK,IAAI;;;;;;YACnE,KAAK;gBACH,qBAAO,8OAAC;oBAAY,MAAM,QAAQ;oBAAW,WAAW;;;;;;YAC1D,KAAK;YACL;gBACE,qBAAO,8OAAC;oBAAe,MAAM,QAAQ;oBAAW,WAAW;;;;;;QAC/D;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;;YACnE;YACA,sBACC,8OAAC;gBAAE,WAAU;gBAA8C,aAAU;0BAClE;;;;;;;;;;;;AAKX;AAGO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAqD;IAC1G,qBAAO,8OAAC;QAAe,MAAM;QAAM,WAAW;;;;;;AAChD;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAA6C;IAC3F,qBAAO,8OAAC;QAAgB,WAAW;QAAY,GAAG,KAAK;;;;;;AACzD;AAEO,SAAS,YAAY,EAAE,OAAO,IAAI,EAAE,SAAS,EAAqD;IACvG,qBAAO,8OAAC;QAAY,MAAM;QAAM,WAAW;;;;;;AAC7C;AAGO,SAAS,aAAa,EAAE,QAAQ,CAAC,EAAE,SAAS,EAA0C;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAEC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,UAAU,QAAQ,IAAI,UAAU,SAAS,uBAAuB;;eAH7D;;;;;;;;;;AASf;AAEO,SAAS,aAAa,EAAE,SAAS,EAA0B;IAChE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;0BAClC,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAgB,WAAU;;;;;;kCAC3B,8OAAC;wBAAgB,WAAU;;;;;;;;;;;;;;;;;;AAInC;AAEO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAqD;IAC1G,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,WAAW,CAAC,KAAK,EAAE;;;;;;AAGvD;AAGO,SAAS,eAAe,EAC7B,SAAS,EACT,OAAO,YAAY,EACnB,SAAS,EAKV;IACC,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yFACA;QAEF,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAe,MAAK;;;;;;8BACrB,8OAAC;oBAAE,WAAU;oBAAsB,aAAU;8BAC1C;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/utils/accessibility.ts"], "sourcesContent": ["// Accessibility utility functions for WCAG 2.1 AA compliance\n\n// Focus management utilities\nexport const trapFocus = (element: HTMLElement): (() => void) => {\n  const focusableElements = element.querySelectorAll(\n    'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n  );\n  \n  const firstFocusableElement = focusableElements[0] as HTMLElement;\n  const lastFocusableElement = focusableElements[focusableElements.length - 1] as HTMLElement;\n\n  const handleTabKey = (e: KeyboardEvent) => {\n    if (e.key !== 'Tab') return;\n\n    if (e.shiftKey) {\n      if (document.activeElement === firstFocusableElement) {\n        lastFocusableElement.focus();\n        e.preventDefault();\n      }\n    } else {\n      if (document.activeElement === lastFocusableElement) {\n        firstFocusableElement.focus();\n        e.preventDefault();\n      }\n    }\n  };\n\n  element.addEventListener('keydown', handleTabKey);\n  firstFocusableElement?.focus();\n\n  return () => {\n    element.removeEventListener('keydown', handleTabKey);\n  };\n};\n\n// Escape key handler for modals and dropdowns\nexport const handleEscapeKey = (callback: () => void) => {\n  const handleKeyDown = (e: KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      callback();\n    }\n  };\n\n  document.addEventListener('keydown', handleKeyDown);\n  \n  return () => {\n    document.removeEventListener('keydown', handleKeyDown);\n  };\n};\n\n// Generate unique IDs for form elements\nexport const generateId = (prefix: string = 'id'): string => {\n  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;\n};\n\n// Screen reader announcements\nexport const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {\n  const announcement = document.createElement('div');\n  announcement.setAttribute('aria-live', priority);\n  announcement.setAttribute('aria-atomic', 'true');\n  announcement.setAttribute('class', 'sr-only');\n  announcement.textContent = message;\n  \n  document.body.appendChild(announcement);\n  \n  setTimeout(() => {\n    document.body.removeChild(announcement);\n  }, 1000);\n};\n\n// Color contrast validation (simplified)\nexport const hasGoodContrast = (foreground: string, background: string): boolean => {\n  // This is a simplified version - in production, use a proper color contrast library\n  // For now, we'll assume our design system colors meet WCAG AA standards\n  return true;\n};\n\n// Skip link functionality\nexport const createSkipLink = (targetId: string, text: string = 'Skip to main content'): HTMLElement => {\n  const skipLink = document.createElement('a');\n  skipLink.href = `#${targetId}`;\n  skipLink.textContent = text;\n  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded';\n  \n  return skipLink;\n};\n\n// Keyboard navigation helpers\nexport const isNavigationKey = (key: string): boolean => {\n  return ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(key);\n};\n\nexport const isActionKey = (key: string): boolean => {\n  return ['Enter', ' '].includes(key);\n};\n\n// ARIA attributes helpers\nexport const getAriaAttributes = (props: {\n  expanded?: boolean;\n  selected?: boolean;\n  disabled?: boolean;\n  required?: boolean;\n  invalid?: boolean;\n  describedBy?: string;\n  labelledBy?: string;\n  controls?: string;\n  owns?: string;\n}) => {\n  const attributes: Record<string, string | boolean> = {};\n  \n  if (props.expanded !== undefined) attributes['aria-expanded'] = props.expanded;\n  if (props.selected !== undefined) attributes['aria-selected'] = props.selected;\n  if (props.disabled !== undefined) attributes['aria-disabled'] = props.disabled;\n  if (props.required !== undefined) attributes['aria-required'] = props.required;\n  if (props.invalid !== undefined) attributes['aria-invalid'] = props.invalid;\n  if (props.describedBy) attributes['aria-describedby'] = props.describedBy;\n  if (props.labelledBy) attributes['aria-labelledby'] = props.labelledBy;\n  if (props.controls) attributes['aria-controls'] = props.controls;\n  if (props.owns) attributes['aria-owns'] = props.owns;\n  \n  return attributes;\n};\n\n// Focus visible utilities\nexport const addFocusVisiblePolyfill = () => {\n  // Simple focus-visible polyfill for older browsers\n  let hadKeyboardEvent = true;\n  \n  const keyboardThrottleTimeout = 100;\n  let keyboardThrottleTimeoutID = 0;\n  \n  const pointerInitialPress = (e: PointerEvent) => {\n    if (e.pointerType === 'mouse' || e.pointerType === 'touch') {\n      hadKeyboardEvent = false;\n    }\n  };\n  \n  const onKeyDown = (e: KeyboardEvent) => {\n    if (e.metaKey || e.altKey || e.ctrlKey) {\n      return;\n    }\n    \n    hadKeyboardEvent = true;\n    clearTimeout(keyboardThrottleTimeoutID);\n    keyboardThrottleTimeoutID = window.setTimeout(() => {\n      hadKeyboardEvent = false;\n    }, keyboardThrottleTimeout);\n  };\n  \n  const onFocus = (e: FocusEvent) => {\n    const target = e.target as HTMLElement;\n    if (hadKeyboardEvent || target.matches(':focus-visible')) {\n      target.classList.add('focus-visible');\n    }\n  };\n  \n  const onBlur = (e: FocusEvent) => {\n    const target = e.target as HTMLElement;\n    target.classList.remove('focus-visible');\n  };\n  \n  document.addEventListener('keydown', onKeyDown, true);\n  document.addEventListener('pointerdown', pointerInitialPress, true);\n  document.addEventListener('focus', onFocus, true);\n  document.addEventListener('blur', onBlur, true);\n};\n\n// Reduced motion detection\nexport const prefersReducedMotion = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n};\n\n// High contrast detection\nexport const prefersHighContrast = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.matchMedia('(prefers-contrast: high)').matches;\n};\n\n// Text size utilities\nexport const getTextSizeClass = (size: 'sm' | 'base' | 'lg' | 'xl'): string => {\n  const sizes = {\n    sm: 'text-sm',\n    base: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n  return sizes[size];\n};\n\n// Semantic HTML helpers\nexport const getSemanticRole = (element: string): string => {\n  const roles: Record<string, string> = {\n    'nav': 'navigation',\n    'main': 'main',\n    'aside': 'complementary',\n    'section': 'region',\n    'article': 'article',\n    'header': 'banner',\n    'footer': 'contentinfo'\n  };\n  \n  return roles[element] || '';\n};\n\n// Form validation messages\nexport const getValidationMessage = (field: string, error: string): string => {\n  const messages: Record<string, Record<string, string>> = {\n    email: {\n      required: 'Email address is required',\n      invalid: 'Please enter a valid email address'\n    },\n    phone: {\n      required: 'Phone number is required',\n      invalid: 'Please enter a valid phone number'\n    },\n    name: {\n      required: 'Name is required',\n      minLength: 'Name must be at least 2 characters long'\n    }\n  };\n  \n  return messages[field]?.[error] || 'This field is invalid';\n};\n"], "names": [], "mappings": "AAAA,6DAA6D;AAE7D,6BAA6B;;;;;;;;;;;;;;;;;;AACtB,MAAM,YAAY,CAAC;IACxB,MAAM,oBAAoB,QAAQ,gBAAgB,CAChD;IAGF,MAAM,wBAAwB,iBAAiB,CAAC,EAAE;IAClD,MAAM,uBAAuB,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;IAE5E,MAAM,eAAe,CAAC;QACpB,IAAI,EAAE,GAAG,KAAK,OAAO;QAErB,IAAI,EAAE,QAAQ,EAAE;YACd,IAAI,SAAS,aAAa,KAAK,uBAAuB;gBACpD,qBAAqB,KAAK;gBAC1B,EAAE,cAAc;YAClB;QACF,OAAO;YACL,IAAI,SAAS,aAAa,KAAK,sBAAsB;gBACnD,sBAAsB,KAAK;gBAC3B,EAAE,cAAc;YAClB;QACF;IACF;IAEA,QAAQ,gBAAgB,CAAC,WAAW;IACpC,uBAAuB;IAEvB,OAAO;QACL,QAAQ,mBAAmB,CAAC,WAAW;IACzC;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,SAAS,gBAAgB,CAAC,WAAW;IAErC,OAAO;QACL,SAAS,mBAAmB,CAAC,WAAW;IAC1C;AACF;AAGO,MAAM,aAAa,CAAC,SAAiB,IAAI;IAC9C,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC/D;AAGO,MAAM,yBAAyB,CAAC,SAAiB,WAAmC,QAAQ;IACjG,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,YAAY,CAAC,aAAa;IACvC,aAAa,YAAY,CAAC,eAAe;IACzC,aAAa,YAAY,CAAC,SAAS;IACnC,aAAa,WAAW,GAAG;IAE3B,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,WAAW;QACT,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG;AACL;AAGO,MAAM,kBAAkB,CAAC,YAAoB;IAClD,oFAAoF;IACpF,wEAAwE;IACxE,OAAO;AACT;AAGO,MAAM,iBAAiB,CAAC,UAAkB,OAAe,sBAAsB;IACpF,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,IAAI,GAAG,CAAC,CAAC,EAAE,UAAU;IAC9B,SAAS,WAAW,GAAG;IACvB,SAAS,SAAS,GAAG;IAErB,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO;QAAC;QAAW;QAAa;QAAa;QAAc;QAAQ;KAAM,CAAC,QAAQ,CAAC;AACrF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO;QAAC;QAAS;KAAI,CAAC,QAAQ,CAAC;AACjC;AAGO,MAAM,oBAAoB,CAAC;IAWhC,MAAM,aAA+C,CAAC;IAEtD,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,OAAO,KAAK,WAAW,UAAU,CAAC,eAAe,GAAG,MAAM,OAAO;IAC3E,IAAI,MAAM,WAAW,EAAE,UAAU,CAAC,mBAAmB,GAAG,MAAM,WAAW;IACzE,IAAI,MAAM,UAAU,EAAE,UAAU,CAAC,kBAAkB,GAAG,MAAM,UAAU;IACtE,IAAI,MAAM,QAAQ,EAAE,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAChE,IAAI,MAAM,IAAI,EAAE,UAAU,CAAC,YAAY,GAAG,MAAM,IAAI;IAEpD,OAAO;AACT;AAGO,MAAM,0BAA0B;IACrC,mDAAmD;IACnD,IAAI,mBAAmB;IAEvB,MAAM,0BAA0B;IAChC,IAAI,4BAA4B;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,SAAS;YAC1D,mBAAmB;QACrB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,EAAE,OAAO,IAAI,EAAE,MAAM,IAAI,EAAE,OAAO,EAAE;YACtC;QACF;QAEA,mBAAmB;QACnB,aAAa;QACb,4BAA4B,OAAO,UAAU,CAAC;YAC5C,mBAAmB;QACrB,GAAG;IACL;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,EAAE,MAAM;QACvB,IAAI,oBAAoB,OAAO,OAAO,CAAC,mBAAmB;YACxD,OAAO,SAAS,CAAC,GAAG,CAAC;QACvB;IACF;IAEA,MAAM,SAAS,CAAC;QACd,MAAM,SAAS,EAAE,MAAM;QACvB,OAAO,SAAS,CAAC,MAAM,CAAC;IAC1B;IAEA,SAAS,gBAAgB,CAAC,WAAW,WAAW;IAChD,SAAS,gBAAgB,CAAC,eAAe,qBAAqB;IAC9D,SAAS,gBAAgB,CAAC,SAAS,SAAS;IAC5C,SAAS,gBAAgB,CAAC,QAAQ,QAAQ;AAC5C;AAGO,MAAM,uBAAuB;IAClC,wCAAmC,OAAO;;;AAE5C;AAGO,MAAM,sBAAsB;IACjC,wCAAmC,OAAO;;;AAE5C;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,QAAQ;QACZ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;IACN;IACA,OAAO,KAAK,CAAC,KAAK;AACpB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,QAAgC;QACpC,OAAO;QACP,QAAQ;QACR,SAAS;QACT,WAAW;QACX,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IAEA,OAAO,KAAK,CAAC,QAAQ,IAAI;AAC3B;AAGO,MAAM,uBAAuB,CAAC,OAAe;IAClD,MAAM,WAAmD;QACvD,OAAO;YACL,UAAU;YACV,SAAS;QACX;QACA,OAAO;YACL,UAAU;YACV,SAAS;QACX;QACA,MAAM;YACJ,UAAU;YACV,WAAW;QACb;IACF;IAEA,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI;AACrC", "debugId": null}}, {"offset": {"line": 2042, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/lib/images.ts"], "sourcesContent": ["// Unsplash image URLs for KostHub\n// All images are optimized for web with proper dimensions and cropping\n\nexport const UNSPLASH_IMAGES = {\n  // Hero section images - modern boarding house exteriors\n  hero: {\n    hero1: \"https://images.unsplash.com/photo-***********35-59a10b8d2000?w=1200&h=800&fit=crop&crop=center\", // Modern building exterior\n    hero2: \"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=800&fit=crop&crop=center\", // Contemporary building\n    hero3: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=1200&h=800&fit=crop&crop=center\", // Urban building\n  },\n\n  // Kost room images - modern, clean boarding house rooms\n  kost: {\n    room1: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center\", // Modern bedroom\n    room2: \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center\", // Cozy bedroom\n    room3: \"https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop&crop=center\", // Minimalist room\n    room4: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center\", // Luxury bedroom\n    room5: \"https://images.unsplash.com/photo-1616594039964-ae9021a400a0?w=800&h=600&fit=crop&crop=center\", // Simple room - FIXED\n    room6: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop&crop=center\", // Contemporary room - FIXED\n\n    // Additional room views\n    interior1: \"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop&crop=center\", // Living area\n    interior2: \"https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop&crop=center\", // Kitchen area - FIXED\n    interior3: \"https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop&crop=center\", // Study area - FIXED\n  },\n  \n  // User avatars - diverse, professional headshots\n  avatars: {\n    male1: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center\", // Professional male\n    female1: \"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=100&h=100&fit=crop&crop=center\", // Professional female - FIXED\n    male2: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center\", // Young male\n    female2: \"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=center\", // Young female - FIXED\n    male3: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=center\", // Casual male\n    female3: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=center\", // Casual female - MOVED\n    male4: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=center\", // Business male\n    female4: \"https://images.unsplash.com/photo-1607746882042-944635dfe10e?w=100&h=100&fit=crop&crop=center\", // Business female - FIXED\n    male5: \"https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=100&h=100&fit=crop&crop=center\", // Friendly male\n    female5: \"https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=100&h=100&fit=crop&crop=center\", // Friendly female - FIXED\n  },\n  \n  // Open Graph images for social sharing\n  og: {\n    main: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center\", // Main OG image\n    listings: \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=1200&h=630&fit=crop&crop=center\", // Listings page\n    about: \"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=1200&h=630&fit=crop&crop=center\", // About page\n    contact: \"https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1200&h=630&fit=crop&crop=center\", // Contact page - FIXED\n  },\n  \n  // Building exteriors for kost locations\n  buildings: {\n    jakarta: \"https://images.unsplash.com/photo-***********35-59a10b8d2000?w=800&h=600&fit=crop&crop=center\", // Jakarta building\n    bandung: \"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center\", // Bandung building\n    yogya: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop&crop=center\", // Yogya building\n    surabaya: \"https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=800&h=600&fit=crop&crop=center\", // Surabaya building\n  },\n  \n  // Facility images\n  facilities: {\n    wifi: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center\", // WiFi setup\n    parking: \"https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=400&h=300&fit=crop&crop=center\", // Parking area\n    kitchen: \"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center\", // Kitchen\n    security: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center\", // Security system\n    laundry: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center\", // Laundry area\n  }\n} as const\n\n// Helper functions for getting images\nexport const getKostImage = (index: number = 0): string => {\n  const images = Object.values(UNSPLASH_IMAGES.kost)\n  return images[index % images.length]\n}\n\nexport const getAvatarImage = (index: number = 0): string => {\n  const avatars = Object.values(UNSPLASH_IMAGES.avatars)\n  return avatars[index % avatars.length]\n}\n\nexport const getRandomKostImages = (count: number = 3): string[] => {\n  const images = Object.values(UNSPLASH_IMAGES.kost)\n  const shuffled = [...images].sort(() => 0.5 - Math.random())\n  return shuffled.slice(0, count)\n}\n\n// Default fallback images\nexport const DEFAULT_KOST_IMAGE = UNSPLASH_IMAGES.kost.room1\nexport const DEFAULT_AVATAR_IMAGE = UNSPLASH_IMAGES.avatars.male1\n\n// Fallback images for error cases\nexport const FALLBACK_IMAGES = {\n  kost: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&crop=center\",\n  avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center\",\n  og: \"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1200&h=630&fit=crop&crop=center\"\n} as const\n\n// Image optimization parameters\nexport const IMAGE_PARAMS = {\n  quality: 80,\n  format: 'webp',\n  sizes: {\n    thumbnail: 'w=300&h=200',\n    card: 'w=400&h=300', \n    preview: 'w=800&h=600',\n    hero: 'w=1200&h=800',\n    og: 'w=1200&h=630',\n    avatar: 'w=100&h=100'\n  }\n} as const\n\n// Function to build optimized image URL\nexport const buildImageUrl = (baseUrl: string, size: keyof typeof IMAGE_PARAMS.sizes): string => {\n  const params = IMAGE_PARAMS.sizes[size]\n  const separator = baseUrl.includes('?') ? '&' : '?'\n  return `${baseUrl}${separator}${params}&fit=crop&crop=center&q=${IMAGE_PARAMS.quality}`\n}\n\n// Function to get safe image URL with fallback\nexport const getSafeImageUrl = (imageUrl: string, type: 'kost' | 'avatar' | 'og' = 'kost'): string => {\n  if (!imageUrl) {\n    return FALLBACK_IMAGES[type]\n  }\n\n  // Validate Unsplash URL format\n  if (!imageUrl.includes('images.unsplash.com')) {\n    return FALLBACK_IMAGES[type]\n  }\n\n  return imageUrl\n}\n\n// Preload critical images\nexport const PRELOAD_IMAGES = [\n  UNSPLASH_IMAGES.kost.room1,\n  UNSPLASH_IMAGES.kost.room2,\n  UNSPLASH_IMAGES.kost.room3,\n] as const\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,uEAAuE;;;;;;;;;;;;;;AAEhE,MAAM,kBAAkB;IAC7B,wDAAwD;IACxD,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IAEA,wDAAwD;IACxD,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QAEP,wBAAwB;QACxB,WAAW;QACX,WAAW;QACX,WAAW;IACb;IAEA,iDAAiD;IACjD,SAAS;QACP,OAAO;QACP,SAAS;QACT,OAAO;QACP,SAAS;QACT,OAAO;QACP,SAAS;QACT,OAAO;QACP,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,uCAAuC;IACvC,IAAI;QACF,MAAM;QACN,UAAU;QACV,OAAO;QACP,SAAS;IACX;IAEA,wCAAwC;IACxC,WAAW;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IAEA,kBAAkB;IAClB,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAGO,MAAM,eAAe,CAAC,QAAgB,CAAC;IAC5C,MAAM,SAAS,OAAO,MAAM,CAAC,gBAAgB,IAAI;IACjD,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;AACtC;AAEO,MAAM,iBAAiB,CAAC,QAAgB,CAAC;IAC9C,MAAM,UAAU,OAAO,MAAM,CAAC,gBAAgB,OAAO;IACrD,OAAO,OAAO,CAAC,QAAQ,QAAQ,MAAM,CAAC;AACxC;AAEO,MAAM,sBAAsB,CAAC,QAAgB,CAAC;IACnD,MAAM,SAAS,OAAO,MAAM,CAAC,gBAAgB,IAAI;IACjD,MAAM,WAAW;WAAI;KAAO,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACzD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,MAAM,qBAAqB,gBAAgB,IAAI,CAAC,KAAK;AACrD,MAAM,uBAAuB,gBAAgB,OAAO,CAAC,KAAK;AAG1D,MAAM,kBAAkB;IAC7B,MAAM;IACN,QAAQ;IACR,IAAI;AACN;AAGO,MAAM,eAAe;IAC1B,SAAS;IACT,QAAQ;IACR,OAAO;QACL,WAAW;QACX,MAAM;QACN,SAAS;QACT,MAAM;QACN,IAAI;QACJ,QAAQ;IACV;AACF;AAGO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,MAAM,SAAS,aAAa,KAAK,CAAC,KAAK;IACvC,MAAM,YAAY,QAAQ,QAAQ,CAAC,OAAO,MAAM;IAChD,OAAO,GAAG,UAAU,YAAY,OAAO,wBAAwB,EAAE,aAAa,OAAO,EAAE;AACzF;AAGO,MAAM,kBAAkB,CAAC,UAAkB,OAAiC,MAAM;IACvF,IAAI,CAAC,UAAU;QACb,OAAO,eAAe,CAAC,KAAK;IAC9B;IAEA,+BAA+B;IAC/B,IAAI,CAAC,SAAS,QAAQ,CAAC,wBAAwB;QAC7C,OAAO,eAAe,CAAC,KAAK;IAC9B;IAEA,OAAO;AACT;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB,IAAI,CAAC,KAAK;IAC1B,gBAAgB,IAAI,CAAC,KAAK;IAC1B,gBAAgB,IAAI,CAAC,KAAK;CAC3B", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/boarding-house-landing.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport dynamic from \"next/dynamic\"\nimport { motion } from \"framer-motion\"\nimport { Navigation } from \"./navigation\"\nimport { Footer } from \"./footer\"\nimport { Loading, LoadingOverlay } from \"./ui/loading\"\nimport { prefersReducedMotion } from \"@/utils/accessibility\"\nimport { PRELOAD_IMAGES } from \"@/lib/images\"\n\n// Lazy load sections for better performance\nconst HeroSection = dynamic(() => import(\"./sections/hero\").then(mod => ({ default: mod.HeroSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"min-h-screen\" />,\n  ssr: true\n})\n\nconst RoomsSection = dynamic(() => import(\"./sections/rooms\").then(mod => ({ default: mod.RoomsSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"h-96\" />,\n  ssr: false\n})\n\nconst FacilitiesSection = dynamic(() => import(\"./sections/facilities\").then(mod => ({ default: mod.FacilitiesSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"h-96\" />,\n  ssr: false\n})\n\nconst TestimonialsSection = dynamic(() => import(\"./sections/testimonials\").then(mod => ({ default: mod.TestimonialsSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"h-96\" />,\n  ssr: false\n})\n\ninterface BoardingHouseLandingProps {\n  className?: string;\n}\n\nexport function BoardingHouseLanding({ className }: BoardingHouseLandingProps) {\n  const [isLoading, setIsLoading] = React.useState(true)\n  const [loadedSections, setLoadedSections] = React.useState<Set<string>>(new Set())\n  const [reducedMotion, setReducedMotion] = React.useState(false)\n\n  // Check for reduced motion preference\n  React.useEffect(() => {\n    setReducedMotion(prefersReducedMotion())\n    \n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')\n    const handleChange = () => setReducedMotion(mediaQuery.matches)\n    \n    mediaQuery.addEventListener('change', handleChange)\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [])\n\n  // Handle initial loading\n  React.useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 1000)\n\n    return () => clearTimeout(timer)\n  }, [])\n\n  // Preload critical images\n  React.useEffect(() => {\n    PRELOAD_IMAGES.forEach(src => {\n      const img = new Image()\n      img.src = src\n    })\n  }, [])\n\n  // Intersection Observer for lazy loading sections\n  React.useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            const sectionId = entry.target.id\n            setLoadedSections(prev => new Set([...prev, sectionId]))\n          }\n        })\n      },\n      {\n        rootMargin: '100px 0px',\n        threshold: 0.1\n      }\n    )\n\n    // Observe all sections\n    const sections = document.querySelectorAll('section[id]')\n    sections.forEach(section => observer.observe(section))\n\n    return () => observer.disconnect()\n  }, [])\n\n  // Skip link for accessibility\n  React.useEffect(() => {\n    const skipLink = document.createElement('a')\n    skipLink.href = '#main-content'\n    skipLink.textContent = 'Skip to main content'\n    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded'\n    \n    document.body.insertBefore(skipLink, document.body.firstChild)\n    \n    return () => {\n      if (document.body.contains(skipLink)) {\n        document.body.removeChild(skipLink)\n      }\n    }\n  }, [])\n\n  return (\n    <div className={`min-h-screen bg-white ${className}`}>\n      {/* Loading Overlay */}\n      <LoadingOverlay \n        isVisible={isLoading} \n        text=\"Welcome to BoardingHouse\"\n      />\n\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Main Content */}\n      <main id=\"main-content\" role=\"main\">\n        {/* Hero Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"min-h-screen\" />}>\n          <HeroSection />\n        </React.Suspense>\n\n        {/* Rooms Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"h-96\" />}>\n          <RoomsSection />\n        </React.Suspense>\n\n        {/* Facilities Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"h-96\" />}>\n          <FacilitiesSection />\n        </React.Suspense>\n\n        {/* Testimonials Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"h-96\" />}>\n          <TestimonialsSection />\n        </React.Suspense>\n      </main>\n\n      {/* Footer */}\n      <Footer />\n\n      {/* Performance Monitoring (Development Only) */}\n      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}\n    </div>\n  )\n}\n\n// Performance monitoring component for development\nfunction PerformanceMonitor() {\n  const [metrics, setMetrics] = React.useState<any>(null)\n\n  React.useEffect(() => {\n    if (typeof window === 'undefined' || !('performance' in window)) return\n\n    const observer = new PerformanceObserver((list) => {\n      const entries = list.getEntries()\n      entries.forEach(entry => {\n        console.log(`Performance: ${entry.name} - ${entry.duration}ms`)\n      })\n    })\n\n    observer.observe({ entryTypes: ['measure', 'navigation'] })\n\n    // Monitor memory usage if available\n    if ('memory' in performance) {\n      const updateMemory = () => {\n        const memory = (performance as any).memory\n        setMetrics({\n          usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576),\n          totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576),\n          jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576)\n        })\n      }\n\n      updateMemory()\n      const interval = setInterval(updateMemory, 5000)\n\n      return () => {\n        clearInterval(interval)\n        observer.disconnect()\n      }\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  if (!metrics) return null\n\n  return (\n    <div className=\"fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs z-50\">\n      <div>Memory: {metrics.usedJSHeapSize}MB / {metrics.totalJSHeapSize}MB</div>\n      <div>Limit: {metrics.jsHeapSizeLimit}MB</div>\n    </div>\n  )\n}\n\n// Error Boundary for better error handling\nexport class BoardingHouseErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('BoardingHouse Error:', error, errorInfo)\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"text-center space-y-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Something went wrong</h1>\n            <p className=\"text-gray-600\">We're sorry, but something unexpected happened.</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n            >\n              Reload Page\n            </button>\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// HOC for performance optimization\nexport function withPerformanceOptimization<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const OptimizedComponent = React.memo((props: P) => {\n    return <Component {...props} />\n  })\n\n  OptimizedComponent.displayName = `withPerformanceOptimization(${Component.displayName || Component.name})`\n  \n  return OptimizedComponent\n}\n\n// Custom hook for intersection observer\nexport function useIntersectionObserver(\n  elementRef: React.RefObject<Element>,\n  options: IntersectionObserverInit = {}\n) {\n  const [isIntersecting, setIsIntersecting] = React.useState(false)\n\n  React.useEffect(() => {\n    const element = elementRef.current\n    if (!element) return\n\n    const observer = new IntersectionObserver(\n      ([entry]) => setIsIntersecting(entry.isIntersecting),\n      {\n        threshold: 0.1,\n        rootMargin: '50px',\n        ...options\n      }\n    )\n\n    observer.observe(element)\n    return () => observer.unobserve(element)\n  }, [elementRef, options])\n\n  return isIntersecting\n}\n\n// Service Worker registration for PWA capabilities\nexport function registerServiceWorker() {\n  if (\n    typeof window !== 'undefined' &&\n    'serviceWorker' in navigator &&\n    process.env.NODE_ENV === 'production'\n  ) {\n    window.addEventListener('load', async () => {\n      try {\n        const registration = await navigator.serviceWorker.register('/sw.js')\n        console.log('SW registered: ', registration)\n      } catch (registrationError) {\n        console.log('SW registration failed: ', registrationError)\n      }\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;AATA;;;;;;;;;AAWA,4CAA4C;AAC5C,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,oJAA0B,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,WAAW;QAAC,CAAC;;;;;;IACnG,SAAS,kBAAM,8OAAC,4HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;AAGP,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACzB,SAAS,kBAAM,8OAAC,4HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;AAGP,MAAM,oBAAoB,CAAA,GAAA,+JAA<PERSON>,CAAA,UAAO,AAAD;;;;;;IAC9B,SAAS,kBAAM,8OAAC,4HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;AAGP,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,SAAS,kBAAM,8OAAC,4HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;AAOA,SAAS,qBAAqB,EAAE,SAAS,EAA6B;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,WAAc,CAAC;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,qMAAA,CAAA,WAAc,CAAc,IAAI;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEzD,sCAAsC;IACtC,qMAAA,CAAA,YAAe,CAAC;QACd,iBAAiB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;QAEpC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,eAAe,IAAM,iBAAiB,WAAW,OAAO;QAE9D,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,yBAAyB;IACzB,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,0BAA0B;IAC1B,qMAAA,CAAA,YAAe,CAAC;QACd,6GAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,CAAA;YACrB,MAAM,MAAM,IAAI;YAChB,IAAI,GAAG,GAAG;QACZ;IACF,GAAG,EAAE;IAEL,kDAAkD;IAClD,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,YAAY,MAAM,MAAM,CAAC,EAAE;oBACjC,kBAAkB,CAAA,OAAQ,IAAI,IAAI;+BAAI;4BAAM;yBAAU;gBACxD;YACF;QACF,GACA;YACE,YAAY;YACZ,WAAW;QACb;QAGF,uBAAuB;QACvB,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAC3C,SAAS,OAAO,CAAC,CAAA,UAAW,SAAS,OAAO,CAAC;QAE7C,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,8BAA8B;IAC9B,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,IAAI,GAAG;QAChB,SAAS,WAAW,GAAG;QACvB,SAAS,SAAS,GAAG;QAErB,SAAS,IAAI,CAAC,YAAY,CAAC,UAAU,SAAS,IAAI,CAAC,UAAU;QAE7D,OAAO;YACL,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW;gBACpC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;;0BAElD,8OAAC,4HAAA,CAAA,iBAAc;gBACb,WAAW;gBACX,MAAK;;;;;;0BAIP,8OAAC,yHAAA,CAAA,aAAU;;;;;0BAGX,8OAAC;gBAAK,IAAG;gBAAe,MAAK;;kCAE3B,8OAAC,qMAAA,CAAA,WAAc;wBAAC,wBAAU,8OAAC,4HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,8OAAC;;;;;;;;;;kCAIH,8OAAC,qMAAA,CAAA,WAAc;wBAAC,wBAAU,8OAAC,4HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,8OAAC;;;;;;;;;;kCAIH,8OAAC,qMAAA,CAAA,WAAc;wBAAC,wBAAU,8OAAC,4HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,8OAAC;;;;;;;;;;kCAIH,8OAAC,qMAAA,CAAA,WAAc;wBAAC,wBAAU,8OAAC,4HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,8OAAC;;;;;;;;;;;;;;;;0BAKL,8OAAC,qHAAA,CAAA,SAAM;;;;;YAGN,oDAAyB,+BAAiB,8OAAC;;;;;;;;;;;AAGlD;AAEA,mDAAmD;AACnD,SAAS;IACP,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,WAAc,CAAM;IAElD,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,gBAAkB,eAAe,CAAC,CAAC,iBAAiB,MAAM,GAAG;;;QAEjE,MAAM;IA8BR,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;oBAAI;oBAAS,QAAQ,cAAc;oBAAC;oBAAM,QAAQ,eAAe;oBAAC;;;;;;;0BACnE,8OAAC;;oBAAI;oBAAQ,QAAQ,eAAe;oBAAC;;;;;;;;;;;;;AAG3C;AAGO,MAAM,mCAAmC,qMAAA,CAAA,YAAe;IAI7D,YAAY,KAAoC,CAAE;QAChD,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAE;QAC5C,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,wBAAwB,OAAO;IAC/C;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS,4BACd,SAAiC;IAEjC,MAAM,mCAAqB,qMAAA,CAAA,OAAU,CAAC,CAAC;QACrC,qBAAO,8OAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;IAEA,mBAAmB,WAAW,GAAG,CAAC,4BAA4B,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE1G,OAAO;AACT;AAGO,SAAS,wBACd,UAAoC,EACpC,UAAoC,CAAC,CAAC;IAEtC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,qMAAA,CAAA,WAAc,CAAC;IAE3D,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,UAAU,WAAW,OAAO;QAClC,IAAI,CAAC,SAAS;QAEd,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM,GAAK,kBAAkB,MAAM,cAAc,GACnD;YACE,WAAW;YACX,YAAY;YACZ,GAAG,OAAO;QACZ;QAGF,SAAS,OAAO,CAAC;QACjB,OAAO,IAAM,SAAS,SAAS,CAAC;IAClC,GAAG;QAAC;QAAY;KAAQ;IAExB,OAAO;AACT;AAGO,SAAS;IACd,IACE,gBAAkB,eAClB,mBAAmB,aACnB,oDAAyB;;AAW7B", "debugId": null}}]}