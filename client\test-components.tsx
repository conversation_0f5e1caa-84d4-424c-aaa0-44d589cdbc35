// Test file to verify component functionality
// Run this in development to test individual components

import React from 'react'
import { But<PERSON> } from './components/ui/button'
import { Card } from './components/ui/card'
import { Modal } from './components/ui/modal'
import { Input } from './components/ui/input'
import { Loading } from './components/ui/loading'
import { ScrollAnimation } from './components/ui/scroll-animation'
import { useModal, useForm } from './hooks'

// Test Button Component
export function TestButton() {
  return (
    <div className="space-y-4 p-4">
      <h2 className="text-2xl font-bold">Button Tests</h2>
      <div className="flex gap-4 flex-wrap">
        <Button variant="default">Default Button</Button>
        <Button variant="secondary">Secondary Button</Button>
        <Button variant="outline">Outline Button</Button>
        <Button variant="ghost">Ghost Button</Button>
        <Button loading>Loading Button</Button>
        <Button disabled>Disabled <PERSON><PERSON></Button>
      </div>
    </div>
  )
}

// Test Card Component
export function TestCard() {
  return (
    <div className="space-y-4 p-4">
      <h2 className="text-2xl font-bold">Card Tests</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card variant="default" className="p-4">
          <h3 className="font-semibold">Default Card</h3>
          <p className="text-gray-600">This is a default card variant.</p>
        </Card>
        <Card variant="elevated" hover className="p-4">
          <h3 className="font-semibold">Elevated Card</h3>
          <p className="text-gray-600">This card has elevation and hover effects.</p>
        </Card>
        <Card variant="outlined" className="p-4">
          <h3 className="font-semibold">Outlined Card</h3>
          <p className="text-gray-600">This card has a border outline.</p>
        </Card>
      </div>
    </div>
  )
}

// Test Modal Component
export function TestModal() {
  const { isOpen, openModal, closeModal } = useModal()

  return (
    <div className="space-y-4 p-4">
      <h2 className="text-2xl font-bold">Modal Tests</h2>
      <Button onClick={openModal}>Open Modal</Button>
      
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        title="Test Modal"
        size="md"
      >
        <div className="space-y-4">
          <p>This is a test modal with various content.</p>
          <div className="flex gap-2">
            <Button onClick={closeModal}>Close</Button>
            <Button variant="outline">Secondary Action</Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

// Test Input Component
export function TestInput() {
  const { values, errors, setValue, setFieldTouched } = useForm({
    email: '',
    name: '',
    phone: ''
  }, {
    email: (value) => {
      if (!value) return 'Email is required'
      if (!/\S+@\S+\.\S+/.test(value)) return 'Invalid email format'
      return null
    },
    name: (value) => {
      if (!value) return 'Name is required'
      if (value.length < 2) return 'Name must be at least 2 characters'
      return null
    }
  })

  return (
    <div className="space-y-4 p-4 max-w-md">
      <h2 className="text-2xl font-bold">Input Tests</h2>
      
      <Input
        label="Name"
        value={values.name}
        onChange={(value) => setValue('name', value)}
        onBlur={() => setFieldTouched('name')}
        error={errors.name}
        required
        placeholder="Enter your name"
      />
      
      <Input
        label="Email"
        type="email"
        value={values.email}
        onChange={(value) => setValue('email', value)}
        onBlur={() => setFieldTouched('email')}
        error={errors.email}
        required
        placeholder="Enter your email"
      />
      
      <Input
        label="Phone"
        type="tel"
        value={values.phone}
        onChange={(value) => setValue('phone', value)}
        placeholder="Enter your phone number"
        helperText="Optional: We'll use this for booking confirmations"
      />
    </div>
  )
}

// Test Loading Component
export function TestLoading() {
  return (
    <div className="space-y-8 p-4">
      <h2 className="text-2xl font-bold">Loading Tests</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Spinner Loading</h3>
        <div className="flex gap-4 items-center">
          <Loading variant="spinner" size="sm" />
          <Loading variant="spinner" size="md" />
          <Loading variant="spinner" size="lg" />
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Dots Loading</h3>
        <div className="flex gap-4 items-center">
          <Loading variant="dots" size="sm" />
          <Loading variant="dots" size="md" />
          <Loading variant="dots" size="lg" />
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Skeleton Loading</h3>
        <div className="space-y-2">
          <Loading variant="skeleton" className="h-4 w-full" />
          <Loading variant="skeleton" className="h-4 w-3/4" />
          <Loading variant="skeleton" className="h-4 w-1/2" />
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Loading with Text</h3>
        <Loading variant="spinner" text="Loading rooms..." />
      </div>
    </div>
  )
}

// Test Scroll Animation Component
export function TestScrollAnimation() {
  return (
    <div className="space-y-16 p-4">
      <h2 className="text-2xl font-bold">Scroll Animation Tests</h2>
      
      <ScrollAnimation animation="fadeIn">
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-2">Fade In Animation</h3>
          <p>This card fades in when scrolled into view.</p>
        </Card>
      </ScrollAnimation>
      
      <ScrollAnimation animation="slideUp" delay={0.2}>
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-2">Slide Up Animation</h3>
          <p>This card slides up with a delay when scrolled into view.</p>
        </Card>
      </ScrollAnimation>
      
      <ScrollAnimation animation="slideLeft">
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-2">Slide Left Animation</h3>
          <p>This card slides in from the left when scrolled into view.</p>
        </Card>
      </ScrollAnimation>
      
      <ScrollAnimation animation="scale" duration={0.8}>
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-2">Scale Animation</h3>
          <p>This card scales in with a longer duration when scrolled into view.</p>
        </Card>
      </ScrollAnimation>
    </div>
  )
}

// Main Test Component
export function ComponentTests() {
  const [activeTest, setActiveTest] = React.useState<string>('button')

  const tests = [
    { id: 'button', label: 'Button', component: TestButton },
    { id: 'card', label: 'Card', component: TestCard },
    { id: 'modal', label: 'Modal', component: TestModal },
    { id: 'input', label: 'Input', component: TestInput },
    { id: 'loading', label: 'Loading', component: TestLoading },
    { id: 'animation', label: 'Animations', component: TestScrollAnimation }
  ]

  const ActiveComponent = tests.find(test => test.id === activeTest)?.component || TestButton

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-bold">Component Tests</h1>
            <div className="flex gap-2">
              {tests.map(test => (
                <Button
                  key={test.id}
                  variant={activeTest === test.id ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTest(test.id)}
                >
                  {test.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="container mx-auto py-8">
        <ActiveComponent />
      </main>
    </div>
  )
}

export default ComponentTests
