{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/boarding-house-landing.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BoardingHouseErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call BoardingHouseErrorBoundary() from the server but BoardingHouseErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx <module evaluation>\",\n    \"BoardingHouseErrorBoundary\",\n);\nexport const BoardingHouseLanding = registerClientReference(\n    function() { throw new Error(\"Attempted to call BoardingHouseLanding() from the server but BoardingHouseLanding is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx <module evaluation>\",\n    \"BoardingHouseLanding\",\n);\nexport const registerServiceWorker = registerClientReference(\n    function() { throw new Error(\"Attempted to call registerServiceWorker() from the server but registerServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx <module evaluation>\",\n    \"registerServiceWorker\",\n);\nexport const useIntersectionObserver = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIntersectionObserver() from the server but useIntersectionObserver is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx <module evaluation>\",\n    \"useIntersectionObserver\",\n);\nexport const withPerformanceOptimization = registerClientReference(\n    function() { throw new Error(\"Attempted to call withPerformanceOptimization() from the server but withPerformanceOptimization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx <module evaluation>\",\n    \"withPerformanceOptimization\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,uEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,uEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,uEACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,uEACA;AAEG,MAAM,8BAA8B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7D;IAAa,MAAM,IAAI,MAAM;AAAsQ,GACnS,uEACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/boarding-house-landing.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BoardingHouseErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call BoardingHouseErrorBoundary() from the server but BoardingHouseErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx\",\n    \"BoardingHouseErrorBoundary\",\n);\nexport const BoardingHouseLanding = registerClientReference(\n    function() { throw new Error(\"Attempted to call BoardingHouseLanding() from the server but BoardingHouseLanding is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx\",\n    \"BoardingHouseLanding\",\n);\nexport const registerServiceWorker = registerClientReference(\n    function() { throw new Error(\"Attempted to call registerServiceWorker() from the server but registerServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx\",\n    \"registerServiceWorker\",\n);\nexport const useIntersectionObserver = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIntersectionObserver() from the server but useIntersectionObserver is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx\",\n    \"useIntersectionObserver\",\n);\nexport const withPerformanceOptimization = registerClientReference(\n    function() { throw new Error(\"Attempted to call withPerformanceOptimization() from the server but withPerformanceOptimization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/boarding-house-landing.tsx\",\n    \"withPerformanceOptimization\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,mDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,mDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,mDACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,mDACA;AAEG,MAAM,8BAA8B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7D;IAAa,MAAM,IAAI,MAAM;AAAsQ,GACnS,mDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/app/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\"\r\nimport { BoardingHouseLanding, BoardingHouseErrorBoundary } from \"@/components/boarding-house-landing\"\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"BoardingHouse - Find Your Ideal Boarding House\",\r\n  description: \"Experience comfortable living with modern amenities, 24/7 security, and a vibrant community. Perfect for students and young professionals in Jakarta.\",\r\n  keywords: [\"boarding house\", \"kost\", \"accommodation\", \"Jakarta\", \"student housing\", \"modern facilities\"],\r\n  authors: [{ name: \"BoardingHouse Team\" }],\r\n  openGraph: {\r\n    title: \"BoardingHouse - Find Your Ideal Boarding House\",\r\n    description: \"Experience comfortable living with modern amenities, 24/7 security, and a vibrant community.\",\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n    siteName: \"BoardingHouse\",\r\n  },\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: \"BoardingHouse - Find Your Ideal Boarding House\",\r\n    description: \"Experience comfortable living with modern amenities, 24/7 security, and a vibrant community.\",\r\n  },\r\n  robots: {\r\n    index: true,\r\n    follow: true,\r\n    googleBot: {\r\n      index: true,\r\n      follow: true,\r\n      \"max-video-preview\": -1,\r\n      \"max-image-preview\": \"large\",\r\n      \"max-snippet\": -1,\r\n    },\r\n  },\r\n  verification: {\r\n    google: \"your-google-verification-code\",\r\n  },\r\n}\r\n\r\nexport default function Home() {\r\n  return (\r\n    <BoardingHouseErrorBoundary>\r\n      <BoardingHouseLanding />\r\n    </BoardingHouseErrorBoundary>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAkB;QAAQ;QAAiB;QAAW;QAAmB;KAAoB;IACxG,SAAS;QAAC;YAAE,MAAM;QAAqB;KAAE;IACzC,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IACV;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC,2IAAA,CAAA,6BAA0B;kBACzB,cAAA,8OAAC,2IAAA,CAAA,uBAAoB;;;;;;;;;;AAG3B", "debugId": null}}]}