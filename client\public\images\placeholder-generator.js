// Placeholder image generator for development
// This script generates placeholder images for the boarding house landing page

const placeholderImages = {
  // Hero section images
  'hero-1.jpg': {
    width: 1920,
    height: 1080,
    text: 'Modern Boarding House',
    background: '#2563eb'
  },
  'hero-2.jpg': {
    width: 1920,
    height: 1080,
    text: 'Comfortable Living',
    background: '#1d4ed8'
  },
  'hero-3.jpg': {
    width: 1920,
    height: 1080,
    text: 'Premium Facilities',
    background: '#1e40af'
  },
  
  // Room images
  'room-single-1.jpg': {
    width: 800,
    height: 600,
    text: 'Single Room',
    background: '#3b82f6'
  },
  'room-single-2.jpg': {
    width: 800,
    height: 600,
    text: 'Single Room Interior',
    background: '#60a5fa'
  },
  'room-single-3.jpg': {
    width: 800,
    height: 600,
    text: 'Single Room Bathroom',
    background: '#93c5fd'
  },
  
  'room-double-1.jpg': {
    width: 800,
    height: 600,
    text: 'Double Room',
    background: '#10b981'
  },
  'room-double-2.jpg': {
    width: 800,
    height: 600,
    text: 'Double Room View',
    background: '#34d399'
  },
  'room-double-3.jpg': {
    width: 800,
    height: 600,
    text: 'Double Room Balcony',
    background: '#6ee7b7'
  },
  
  'room-shared-1.jpg': {
    width: 800,
    height: 600,
    text: 'Shared Room',
    background: '#f59e0b'
  },
  'room-shared-2.jpg': {
    width: 800,
    height: 600,
    text: 'Shared Facilities',
    background: '#fbbf24'
  },
  'room-shared-3.jpg': {
    width: 800,
    height: 600,
    text: 'Common Area',
    background: '#fcd34d'
  },
  
  'room-suite-1.jpg': {
    width: 800,
    height: 600,
    text: 'Executive Suite',
    background: '#8b5cf6'
  },
  'room-suite-2.jpg': {
    width: 800,
    height: 600,
    text: 'Suite Living Area',
    background: '#a78bfa'
  },
  'room-suite-3.jpg': {
    width: 800,
    height: 600,
    text: 'Suite Kitchen',
    background: '#c4b5fd'
  },
  
  // Avatar images
  'avatar-sarah.jpg': {
    width: 200,
    height: 200,
    text: 'Sarah',
    background: '#ec4899'
  },
  'avatar-ahmad.jpg': {
    width: 200,
    height: 200,
    text: 'Ahmad',
    background: '#06b6d4'
  },
  'avatar-maria.jpg': {
    width: 200,
    height: 200,
    text: 'Maria',
    background: '#84cc16'
  },
  'avatar-david.jpg': {
    width: 200,
    height: 200,
    text: 'David',
    background: '#f97316'
  },
  'avatar-lisa.jpg': {
    width: 200,
    height: 200,
    text: 'Lisa',
    background: '#ef4444'
  },
  
  // Placeholder room image
  'placeholder-room.jpg': {
    width: 800,
    height: 600,
    text: 'Loading...',
    background: '#6b7280'
  }
}

// Function to generate placeholder image URL
function generatePlaceholderUrl(filename) {
  const config = placeholderImages[filename]
  if (!config) return null
  
  const { width, height, text, background } = config
  const encodedText = encodeURIComponent(text)
  
  // Using placeholder.com service
  return `https://via.placeholder.com/${width}x${height}/${background.slice(1)}/ffffff?text=${encodedText}`
}

// Export for use in the application
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { placeholderImages, generatePlaceholderUrl }
}

// For browser usage
if (typeof window !== 'undefined') {
  window.placeholderImages = placeholderImages
  window.generatePlaceholderUrl = generatePlaceholderUrl
}

console.log('Placeholder images configuration loaded')
console.log('Available images:', Object.keys(placeholderImages))
