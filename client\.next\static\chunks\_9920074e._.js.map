{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/ui/modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { X } from \"lucide-react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { modalVariants, backdropVariants } from \"@/utils/animations\"\nimport { trapFocus, handleEscapeKey } from \"@/utils/accessibility\"\nimport { Button } from \"./button\"\n\nconst modalVariants_cva = cva(\n  \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n  {\n    variants: {\n      size: {\n        sm: \"max-w-sm\",\n        md: \"max-w-md\",\n        lg: \"max-w-lg\",\n        xl: \"max-w-xl\",\n        full: \"max-w-full\",\n      },\n    },\n    defaultVariants: {\n      size: \"md\",\n    },\n  }\n)\n\ninterface ModalProps extends VariantProps<typeof modalVariants_cva> {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  closeOnOverlayClick?: boolean;\n  showCloseButton?: boolean;\n  className?: string;\n}\n\nexport function Modal({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = \"md\",\n  closeOnOverlayClick = true,\n  showCloseButton = true,\n  className,\n}: ModalProps) {\n  const modalRef = React.useRef<HTMLDivElement>(null);\n  const titleId = React.useId();\n\n  // Handle escape key and focus trapping\n  React.useEffect(() => {\n    if (!isOpen) return;\n\n    const cleanup = handleEscapeKey(onClose);\n    let focusCleanup: (() => void) | undefined;\n\n    if (modalRef.current) {\n      focusCleanup = trapFocus(modalRef.current);\n    }\n\n    return () => {\n      cleanup();\n      focusCleanup?.();\n    };\n  }, [isOpen, onClose]);\n\n  // Prevent body scroll when modal is open\n  React.useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const handleOverlayClick = (e: React.MouseEvent) => {\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50\">\n          {/* Backdrop */}\n          <motion.div\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm\"\n            variants={backdropVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            exit=\"exit\"\n            onClick={handleOverlayClick}\n          />\n          \n          {/* Modal */}\n          <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n            <motion.div\n              ref={modalRef}\n              className={cn(\n                \"relative w-full bg-background rounded-lg shadow-xl border\",\n                modalVariants_cva({ size }),\n                className\n              )}\n              variants={modalVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              exit=\"exit\"\n              role=\"dialog\"\n              aria-modal=\"true\"\n              aria-labelledby={titleId}\n            >\n              {/* Header */}\n              <div className=\"flex items-center justify-between p-6 border-b\">\n                <h2\n                  id={titleId}\n                  className=\"text-lg font-semibold text-foreground\"\n                >\n                  {title}\n                </h2>\n                {showCloseButton && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={onClose}\n                    aria-label=\"Close modal\"\n                    className=\"h-8 w-8 rounded-full\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                {children}\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n}\n\n// Modal components for composition\nexport function ModalHeader({ className, children, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\nexport function ModalTitle({ className, children, ...props }: React.HTMLAttributes<HTMLHeadingElement>) {\n  return (\n    <h3\n      className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\n      {...props}\n    >\n      {children}\n    </h3>\n  );\n}\n\nexport function ModalDescription({ className, children, ...props }: React.HTMLAttributes<HTMLParagraphElement>) {\n  return (\n    <p\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    >\n      {children}\n    </p>\n  );\n}\n\nexport function ModalContent({ className, children, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"grid gap-4 py-4\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\nexport function ModalFooter({ className, children, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAYA,MAAM,oBAAoB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC1B,2DACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAaK,SAAS,MAAM,KAST;QATS,EACpB,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,sBAAsB,IAAI,EAC1B,kBAAkB,IAAI,EACtB,SAAS,EACE,GATS;;IAUpB,MAAM,WAAW,6JAAA,CAAA,SAAY,CAAiB;IAC9C,MAAM,UAAU,6JAAA,CAAA,QAAW;IAE3B,uCAAuC;IACvC,6JAAA,CAAA,YAAe;2BAAC;YACd,IAAI,CAAC,QAAQ;YAEb,MAAM,UAAU,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;YAChC,IAAI;YAEJ,IAAI,SAAS,OAAO,EAAE;gBACpB,eAAe,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE,SAAS,OAAO;YAC3C;YAEA;mCAAO;oBACL;oBACA,yBAAA,mCAAA;gBACF;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,yCAAyC;IACzC,6JAAA,CAAA,YAAe;2BAAC;YACd,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,sHAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6DACA,kBAAkB;4BAAE;wBAAK,IACzB;wBAEF,UAAU,sHAAA,CAAA,gBAAa;wBACvB,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,MAAK;wBACL,cAAW;wBACX,mBAAiB;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,IAAI;wCACJ,WAAU;kDAET;;;;;;oCAEF,iCACC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,cAAW;wCACX,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAMnB,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GA/GgB;KAAA;AAkHT,SAAS,YAAY,KAAuE;QAAvE,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAA6C,GAAvE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,WAAW,KAA2E;QAA3E,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAiD,GAA3E;IACzB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,iBAAiB,KAA6E;QAA7E,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAmD,GAA7E;IAC/B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,aAAa,KAAuE;QAAvE,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAA6C,GAAvE;IAC3B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB;AAWT,SAAS,YAAY,KAAuE;QAAvE,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAA6C,GAAvE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;kBAER;;;;;;AAGP;MATgB", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/utils/performance.ts"], "sourcesContent": ["// Performance optimization utilities\n\nimport { useCallback, useRef, useEffect, useState } from 'react';\n\n// Debounce hook for search inputs and form validation\nexport const useDebounce = <T>(value: T, delay: number): T => {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n};\n\n// Throttle hook for scroll events\nexport const useThrottle = <T extends (...args: any[]) => any>(\n  callback: T,\n  delay: number\n): T => {\n  const throttleRef = useRef<NodeJS.Timeout | null>(null);\n  const lastCallRef = useRef<number>(0);\n\n  return useCallback(\n    ((...args: Parameters<T>) => {\n      const now = Date.now();\n      \n      if (now - lastCallRef.current >= delay) {\n        lastCallRef.current = now;\n        return callback(...args);\n      }\n      \n      if (throttleRef.current) {\n        clearTimeout(throttleRef.current);\n      }\n      \n      throttleRef.current = setTimeout(() => {\n        lastCallRef.current = Date.now();\n        callback(...args);\n      }, delay - (now - lastCallRef.current));\n    }) as T,\n    [callback, delay]\n  );\n};\n\n// Intersection Observer hook for lazy loading\nexport const useIntersectionObserver = (\n  options: IntersectionObserverInit = {}\n) => {\n  const [isIntersecting, setIsIntersecting] = useState(false);\n  const [hasIntersected, setHasIntersected] = useState(false);\n  const targetRef = useRef<HTMLElement | null>(null);\n\n  useEffect(() => {\n    const target = targetRef.current;\n    if (!target) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        setIsIntersecting(entry.isIntersecting);\n        if (entry.isIntersecting && !hasIntersected) {\n          setHasIntersected(true);\n        }\n      },\n      {\n        threshold: 0.1,\n        rootMargin: '50px',\n        ...options,\n      }\n    );\n\n    observer.observe(target);\n\n    return () => {\n      observer.unobserve(target);\n    };\n  }, [hasIntersected, options]);\n\n  return { targetRef, isIntersecting, hasIntersected };\n};\n\n// Image lazy loading with placeholder\nexport const useLazyImage = (src: string, placeholder?: string) => {\n  const [imageSrc, setImageSrc] = useState(placeholder || '');\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [isError, setIsError] = useState(false);\n  const { targetRef, hasIntersected } = useIntersectionObserver();\n\n  useEffect(() => {\n    if (hasIntersected && src) {\n      const img = new Image();\n      img.onload = () => {\n        setImageSrc(src);\n        setIsLoaded(true);\n      };\n      img.onerror = () => {\n        setIsError(true);\n      };\n      img.src = src;\n    }\n  }, [hasIntersected, src]);\n\n  return { targetRef, imageSrc, isLoaded, isError };\n};\n\n// Preload images for better UX\nexport const preloadImages = (urls: string[]): Promise<void[]> => {\n  const promises = urls.map((url) => {\n    return new Promise<void>((resolve, reject) => {\n      const img = new Image();\n      img.onload = () => resolve();\n      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));\n      img.src = url;\n    });\n  });\n\n  return Promise.all(promises);\n};\n\n// Virtual scrolling for large lists\nexport const useVirtualScroll = <T>(\n  items: T[],\n  itemHeight: number,\n  containerHeight: number\n) => {\n  const [scrollTop, setScrollTop] = useState(0);\n  \n  const startIndex = Math.floor(scrollTop / itemHeight);\n  const endIndex = Math.min(\n    startIndex + Math.ceil(containerHeight / itemHeight) + 1,\n    items.length\n  );\n  \n  const visibleItems = items.slice(startIndex, endIndex);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = startIndex * itemHeight;\n\n  return {\n    visibleItems,\n    totalHeight,\n    offsetY,\n    onScroll: (e: React.UIEvent<HTMLDivElement>) => {\n      setScrollTop(e.currentTarget.scrollTop);\n    },\n  };\n};\n\n// Memory usage monitoring (development only)\nexport const useMemoryMonitor = (enabled: boolean = process.env.NODE_ENV === 'development') => {\n  const [memoryInfo, setMemoryInfo] = useState<any>(null);\n\n  useEffect(() => {\n    if (!enabled || !('memory' in performance)) return;\n\n    const updateMemoryInfo = () => {\n      const memory = (performance as any).memory;\n      setMemoryInfo({\n        usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576), // MB\n        totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576), // MB\n        jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB\n      });\n    };\n\n    updateMemoryInfo();\n    const interval = setInterval(updateMemoryInfo, 5000);\n\n    return () => clearInterval(interval);\n  }, [enabled]);\n\n  return memoryInfo;\n};\n\n// Bundle size analyzer helper\nexport const logBundleSize = (componentName: string) => {\n  if (process.env.NODE_ENV === 'development') {\n    console.log(`Component ${componentName} loaded`);\n  }\n};\n\n// Performance timing utilities\nexport const measurePerformance = (name: string, fn: () => void) => {\n  if (process.env.NODE_ENV === 'development') {\n    const start = performance.now();\n    fn();\n    const end = performance.now();\n    console.log(`${name} took ${end - start} milliseconds`);\n  } else {\n    fn();\n  }\n};\n\n// Web Vitals tracking\nexport const trackWebVitals = (metric: any) => {\n  if (process.env.NODE_ENV === 'production') {\n    // In production, you would send this to your analytics service\n    console.log(metric);\n  }\n};\n\n// Image optimization helpers\nexport const getOptimizedImageUrl = (\n  src: string,\n  width: number,\n  quality: number = 75\n): string => {\n  // This would integrate with your image optimization service\n  // For now, return the original src\n  return src;\n};\n\nexport const generateImageSrcSet = (\n  src: string,\n  sizes: number[]\n): string => {\n  return sizes\n    .map(size => `${getOptimizedImageUrl(src, size)} ${size}w`)\n    .join(', ');\n};\n\n// Resource hints for better loading\nexport const addResourceHints = (urls: string[], type: 'preload' | 'prefetch' = 'preload') => {\n  urls.forEach(url => {\n    const link = document.createElement('link');\n    link.rel = type;\n    link.href = url;\n    link.as = 'image';\n    document.head.appendChild(link);\n  });\n};\n\n// Critical CSS detection\nexport const isCriticalCSS = (selector: string): boolean => {\n  // Simple heuristic to determine if CSS is critical\n  const criticalSelectors = [\n    'body', 'html', 'header', 'nav', 'main', 'hero',\n    '.hero', '.header', '.navigation', '.above-fold'\n  ];\n  \n  return criticalSelectors.some(critical => \n    selector.toLowerCase().includes(critical)\n  );\n};\n\n// Service Worker registration\nexport const registerServiceWorker = async () => {\n  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {\n    try {\n      const registration = await navigator.serviceWorker.register('/sw.js');\n      console.log('SW registered: ', registration);\n    } catch (registrationError) {\n      console.log('SW registration failed: ', registrationError);\n    }\n  }\n};\n\n// Cache management\nexport const clearCache = async () => {\n  if ('caches' in window) {\n    const cacheNames = await caches.keys();\n    await Promise.all(\n      cacheNames.map(cacheName => caches.delete(cacheName))\n    );\n  }\n};\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;;;;;;;;;;;;;AA0Je;AAxJpD;;;AAGO,MAAM,cAAc,CAAI,OAAU;;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,UAAU;iDAAW;oBACzB,kBAAkB;gBACpB;gDAAG;YAEH;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;GAda;AAiBN,MAAM,cAAc,CACzB,UACA;;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAEnC,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mCACd;6CAAI;gBAAA;;YACH,MAAM,MAAM,KAAK,GAAG;YAEpB,IAAI,MAAM,YAAY,OAAO,IAAI,OAAO;gBACtC,YAAY,OAAO,GAAG;gBACtB,OAAO,YAAY;YACrB;YAEA,IAAI,YAAY,OAAO,EAAE;gBACvB,aAAa,YAAY,OAAO;YAClC;YAEA,YAAY,OAAO,GAAG;2CAAW;oBAC/B,YAAY,OAAO,GAAG,KAAK,GAAG;oBAC9B,YAAY;gBACd;0CAAG,QAAQ,CAAC,MAAM,YAAY,OAAO;QACvC;kCACA;QAAC;QAAU;KAAM;AAErB;IA3Ba;AA8BN,MAAM,0BAA0B;QACrC,2EAAoC,CAAC;;IAErC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,WAAW,IAAI;qDACnB;wBAAC,CAAC,MAAM;oBACN,kBAAkB,MAAM,cAAc;oBACtC,IAAI,MAAM,cAAc,IAAI,CAAC,gBAAgB;wBAC3C,kBAAkB;oBACpB;gBACF;oDACA;gBACE,WAAW;gBACX,YAAY;gBACZ,GAAG,OAAO;YACZ;YAGF,SAAS,OAAO,CAAC;YAEjB,OAjDA;qDAiDO;oBACL,SAAS,SAAS,CAAC;gBACrB;aAnCC;QAoCH;4CAAG;QAAC;QAAgB;KAAQ;IAE5B,OAAO;QAAE;QAAW;QAAgB;IAAe;AACrD;IAjCa;AAoCN,MAAM,eAAe,CAAC,KAAa;;IACxC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,KAAK;gBACzB,MAAM,MAAM,IAAI;gBAChB,IAAI,MAAM;8CAAG;wBACX,YAAY;wBACZ,YAAY;oBACd;;gBACA,IAAI,OAAO;8CAAG;wBACZ,WAAW;oBACb;;gBACA,IAAI,GAAG,GAAG;YACZ;QACF;iCAAG;QAAC;QAAgB;KAAI;IAExB,OAAO;QAAE;QAAW;QAAU;QAAU;IAAQ;AAClD;IArBa;;QAI2B;;;AAoBjC,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC;QACzB,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,MAAM,MAAM,IAAI;YAChB,IAAI,MAAM,GAAG,IAAM;YACnB,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM,AAAC,yBAA4B,OAAJ;YAC9D,IAAI,GAAG,GAAG;QACZ;IACF;IAEA,OAAO,QAAQ,GAAG,CAAC;AACrB;AAGO,MAAM,mBAAmB,CAC9B,OACA,YACA;;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa,KAAK,KAAK,CAAC,YAAY;IAC1C,MAAM,WAAW,KAAK,GAAG,CACvB,aAAa,KAAK,IAAI,CAAC,kBAAkB,cAAc,GACvD,MAAM,MAAM;IAGd,MAAM,eAAe,MAAM,KAAK,CAAC,YAAY;IAC7C,MAAM,cAAc,MAAM,MAAM,GAAG;IACnC,MAAM,UAAU,aAAa;IAE7B,OAAO;QACL;QACA;QACA;QACA,UAAU,CAAC;YACT,aAAa,EAAE,aAAa,CAAC,SAAS;QACxC;IACF;AACF;IAzBa;AA4BN,MAAM,mBAAmB;QAAC,2EAAmB,oDAAyB;;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,WAAW,GAAG;YAE5C,MAAM;+DAAmB;oBACvB,MAAM,SAAS,AAAC,YAAoB,MAAM;oBAC1C,cAAc;wBACZ,gBAAgB,KAAK,KAAK,CAAC,OAAO,cAAc,GAAG;wBACnD,iBAAiB,KAAK,KAAK,CAAC,OAAO,eAAe,GAAG;wBACrD,iBAAiB,KAAK,KAAK,CAAC,OAAO,eAAe,GAAG;oBACvD;gBACF;;YAEA;YACA,MAAM,WAAW,YAAY,kBAAkB;YAE/C;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAQ;IAEZ,OAAO;AACT;IAtBa;AAyBN,MAAM,gBAAgB,CAAC;IAC5B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,AAAC,aAA0B,OAAd,eAAc;IACzC;AACF;AAGO,MAAM,qBAAqB,CAAC,MAAc;IAC/C,wCAA4C;QAC1C,MAAM,QAAQ,YAAY,GAAG;QAC7B;QACA,MAAM,MAAM,YAAY,GAAG;QAC3B,QAAQ,GAAG,CAAC,AAAC,GAAe,OAAb,MAAK,UAAoB,OAAZ,MAAM,OAAM;IAC1C;;AAGF;AAGO,MAAM,iBAAiB,CAAC;IAC7B;;AAIF;AAGO,MAAM,uBAAuB,SAClC,KACA;QACA,2EAAkB;IAElB,4DAA4D;IAC5D,mCAAmC;IACnC,OAAO;AACT;AAEO,MAAM,sBAAsB,CACjC,KACA;IAEA,OAAO,MACJ,GAAG,CAAC,CAAA,OAAQ,AAAC,GAAqC,OAAnC,qBAAqB,KAAK,OAAM,KAAQ,OAAL,MAAK,MACvD,IAAI,CAAC;AACV;AAGO,MAAM,mBAAmB,SAAC;QAAgB,wEAA+B;IAC9E,KAAK,OAAO,CAAC,CAAA;QACX,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,KAAK,EAAE,GAAG;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAGO,MAAM,gBAAgB,CAAC;IAC5B,mDAAmD;IACnD,MAAM,oBAAoB;QACxB;QAAQ;QAAQ;QAAU;QAAO;QAAQ;QACzC;QAAS;QAAW;QAAe;KACpC;IAED,OAAO,kBAAkB,IAAI,CAAC,CAAA,WAC5B,SAAS,WAAW,GAAG,QAAQ,CAAC;AAEpC;AAGO,MAAM,wBAAwB;IACnC,IAAI,mBAAmB,aAAa,oDAAyB;;AAQ/D;AAGO,MAAM,aAAa;IACxB,IAAI,YAAY,QAAQ;QACtB,MAAM,aAAa,MAAM,OAAO,IAAI;QACpC,MAAM,QAAQ,GAAG,CACf,WAAW,GAAG,CAAC,CAAA,YAAa,OAAO,MAAM,CAAC;IAE9C;AACF", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/hooks/index.ts"], "sourcesContent": ["// Custom hooks for the boarding house application\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { useInView } from 'react-intersection-observer';\nimport type { Room, Testimonial, ContactFormData } from '../types';\n\n// Hook for managing modal state\nexport const useModal = (initialState: boolean = false) => {\n  const [isOpen, setIsOpen] = useState(initialState);\n  const [focusedElementBeforeModal, setFocusedElementBeforeModal] = useState<HTMLElement | null>(null);\n\n  const openModal = useCallback(() => {\n    setFocusedElementBeforeModal(document.activeElement as HTMLElement);\n    setIsOpen(true);\n  }, []);\n\n  const closeModal = useCallback(() => {\n    setIsOpen(false);\n    // Restore focus to the element that opened the modal\n    setTimeout(() => {\n      focusedElementBeforeModal?.focus();\n    }, 100);\n  }, [focusedElementBeforeModal]);\n\n  // Close modal on Escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && isOpen) {\n        closeModal();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, closeModal]);\n\n  return { isOpen, openModal, closeModal };\n};\n\n// Hook for scroll-triggered animations\nexport const useScrollAnimation = (threshold: number = 0.1) => {\n  const { ref, inView, entry } = useInView({\n    threshold,\n    triggerOnce: true,\n    rootMargin: '0px 0px -50px 0px',\n  });\n\n  return { ref, inView, entry };\n};\n\n// Hook for managing form state with validation\nexport const useForm = <T extends Record<string, any>>(\n  initialValues: T,\n  validationRules?: Partial<Record<keyof T, (value: any) => string | null>>\n) => {\n  const [values, setValues] = useState<T>(initialValues);\n  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});\n  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const setValue = useCallback((field: keyof T, value: any) => {\n    setValues(prev => ({ ...prev, [field]: value }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }));\n    }\n  }, [errors]);\n\n  const setFieldTouched = useCallback((field: keyof T) => {\n    setTouched(prev => ({ ...prev, [field]: true }));\n  }, []);\n\n  const validateField = useCallback((field: keyof T, value: any) => {\n    if (!validationRules?.[field]) return null;\n    return validationRules[field]!(value);\n  }, [validationRules]);\n\n  const validateForm = useCallback(() => {\n    const newErrors: Partial<Record<keyof T, string>> = {};\n    let isValid = true;\n\n    Object.keys(values).forEach(key => {\n      const field = key as keyof T;\n      const error = validateField(field, values[field]);\n      if (error) {\n        newErrors[field] = error;\n        isValid = false;\n      }\n    });\n\n    setErrors(newErrors);\n    return isValid;\n  }, [values, validateField]);\n\n  const handleSubmit = useCallback(async (\n    onSubmit: (values: T) => Promise<void> | void\n  ) => {\n    setIsSubmitting(true);\n    \n    // Mark all fields as touched\n    const allTouched = Object.keys(values).reduce((acc, key) => {\n      acc[key as keyof T] = true;\n      return acc;\n    }, {} as Partial<Record<keyof T, boolean>>);\n    setTouched(allTouched);\n\n    if (validateForm()) {\n      try {\n        await onSubmit(values);\n      } catch (error) {\n        console.error('Form submission error:', error);\n      }\n    }\n    \n    setIsSubmitting(false);\n  }, [values, validateForm]);\n\n  const reset = useCallback(() => {\n    setValues(initialValues);\n    setErrors({});\n    setTouched({});\n    setIsSubmitting(false);\n  }, [initialValues]);\n\n  return {\n    values,\n    errors,\n    touched,\n    isSubmitting,\n    setValue,\n    setFieldTouched,\n    validateForm,\n    handleSubmit,\n    reset,\n  };\n};\n\n// Hook for managing room data\nexport const useRooms = () => {\n  const [rooms, setRooms] = useState<Room[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    // Mock data - replace with actual API call\n    const fetchRooms = async () => {\n      try {\n        setLoading(true);\n        // Simulate API delay\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        const mockRooms: Room[] = [\n          {\n            id: '1',\n            title: 'Deluxe Single Room',\n            type: 'single',\n            price: 1500000,\n            currency: 'IDR',\n            images: ['/images/room-1.jpg', '/images/room-1-2.jpg'],\n            features: ['AC', 'Private Bathroom', 'WiFi', 'Desk'],\n            description: 'Comfortable single room with modern amenities',\n            availability: true,\n            size: 12,\n            floor: 2,\n            amenities: []\n          },\n          {\n            id: '2',\n            title: 'Premium Double Room',\n            type: 'double',\n            price: 2000000,\n            currency: 'IDR',\n            images: ['/images/room-2.jpg', '/images/room-2-2.jpg'],\n            features: ['AC', 'Private Bathroom', 'WiFi', 'Balcony'],\n            description: 'Spacious double room with balcony view',\n            availability: true,\n            size: 18,\n            floor: 3,\n            amenities: []\n          }\n        ];\n        \n        setRooms(mockRooms);\n      } catch (err) {\n        setError('Failed to fetch rooms');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchRooms();\n  }, []);\n\n  return { rooms, loading, error };\n};\n\n// Hook for managing testimonials\nexport const useTestimonials = () => {\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  useEffect(() => {\n    // Mock testimonials data\n    const mockTestimonials: Testimonial[] = [\n      {\n        id: '1',\n        name: 'Sarah Johnson',\n        rating: 5,\n        comment: 'Amazing place to stay! Clean, comfortable, and great location.',\n        date: '2024-01-15',\n        verified: true\n      },\n      {\n        id: '2',\n        name: 'Ahmad Rahman',\n        rating: 5,\n        comment: 'Excellent facilities and very friendly staff. Highly recommended!',\n        date: '2024-01-10',\n        verified: true\n      }\n    ];\n    \n    setTestimonials(mockTestimonials);\n  }, []);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying || testimonials.length <= 1) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex(prev => (prev + 1) % testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, testimonials.length]);\n\n  const goToSlide = useCallback((index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  }, []);\n\n  const nextSlide = useCallback(() => {\n    setCurrentIndex(prev => (prev + 1) % testimonials.length);\n  }, [testimonials.length]);\n\n  const prevSlide = useCallback(() => {\n    setCurrentIndex(prev => (prev - 1 + testimonials.length) % testimonials.length);\n  }, [testimonials.length]);\n\n  return {\n    testimonials,\n    currentIndex,\n    isAutoPlaying,\n    goToSlide,\n    nextSlide,\n    prevSlide,\n    setIsAutoPlaying\n  };\n};\n\n// Hook for managing responsive breakpoints\nexport const useBreakpoint = () => {\n  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');\n\n  useEffect(() => {\n    const updateBreakpoint = () => {\n      const width = window.innerWidth;\n      if (width < 768) {\n        setBreakpoint('mobile');\n      } else if (width < 1024) {\n        setBreakpoint('tablet');\n      } else {\n        setBreakpoint('desktop');\n      }\n    };\n\n    updateBreakpoint();\n    window.addEventListener('resize', updateBreakpoint);\n    \n    return () => window.removeEventListener('resize', updateBreakpoint);\n  }, []);\n\n  return breakpoint;\n};\n\n// Hook for managing local storage\nexport const useLocalStorage = <T>(key: string, initialValue: T) => {\n  const [storedValue, setStoredValue] = useState<T>(() => {\n    if (typeof window === 'undefined') return initialValue;\n    \n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? JSON.parse(item) : initialValue;\n    } catch (error) {\n      console.error(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  });\n\n  const setValue = useCallback((value: T | ((val: T) => T)) => {\n    try {\n      const valueToStore = value instanceof Function ? value(storedValue) : value;\n      setStoredValue(valueToStore);\n      \n      if (typeof window !== 'undefined') {\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\n      }\n    } catch (error) {\n      console.error(`Error setting localStorage key \"${key}\":`, error);\n    }\n  }, [key, storedValue]);\n\n  return [storedValue, setValue] as const;\n};\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;AAElD;AACA;;;;AAIO,MAAM,WAAW;QAAC,gFAAwB;;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAE/F,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE;YAC5B,6BAA6B,SAAS,aAAa;YACnD,UAAU;QACZ;0CAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YAC7B,UAAU;YACV,qDAAqD;YACrD;oDAAW;oBACT,sCAAA,gDAAA,0BAA2B,KAAK;gBAClC;mDAAG;QACL;2CAAG;QAAC;KAA0B;IAE9B,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;mDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;wBAChC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;sCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;6BAAG;QAAC;QAAQ;KAAW;IAEvB,OAAO;QAAE;QAAQ;QAAW;IAAW;AACzC;GArCa;AAwCN,MAAM,qBAAqB;QAAC,6EAAoB;;IACrD,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QACvC;QACA,aAAa;QACb,YAAY;IACd;IAEA,OAAO;QAAE;QAAK;QAAQ;IAAM;AAC9B;IARa;;QACoB,sKAAA,CAAA,YAAS;;;AAUnC,MAAM,UAAU,CACrB,eACA;;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IACxC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,CAAC,OAAgB;YAC5C;iDAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,CAAC;;YAE9C,sCAAsC;YACtC,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB;qDAAU,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,MAAM,EAAE;wBAAU,CAAC;;YACpD;QACF;wCAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YACnC;wDAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAK,CAAC;;QAChD;+CAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,OAAgB;YACjD,IAAI,EAAC,4BAAA,sCAAA,eAAiB,CAAC,MAAM,GAAE,OAAO;YACtC,OAAO,eAAe,CAAC,MAAM,CAAE;QACjC;6CAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC/B,MAAM,YAA8C,CAAC;YACrD,IAAI,UAAU;YAEd,OAAO,IAAI,CAAC,QAAQ,OAAO;qDAAC,CAAA;oBAC1B,MAAM,QAAQ;oBACd,MAAM,QAAQ,cAAc,OAAO,MAAM,CAAC,MAAM;oBAChD,IAAI,OAAO;wBACT,SAAS,CAAC,MAAM,GAAG;wBACnB,UAAU;oBACZ;gBACF;;YAEA,UAAU;YACV,OAAO;QACT;4CAAG;QAAC;QAAQ;KAAc;IAE1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAC/B;YAEA,gBAAgB;YAEhB,6BAA6B;YAC7B,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,MAAM;gEAAC,CAAC,KAAK;oBAClD,GAAG,CAAC,IAAe,GAAG;oBACtB,OAAO;gBACT;+DAAG,CAAC;YACJ,WAAW;YAEX,IAAI,gBAAgB;gBAClB,IAAI;oBACF,MAAM,SAAS;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C;YACF;YAEA,gBAAgB;QAClB;4CAAG;QAAC;QAAQ;KAAa;IAEzB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sCAAE;YACxB,UAAU;YACV,UAAU,CAAC;YACX,WAAW,CAAC;YACZ,gBAAgB;QAClB;qCAAG;QAAC;KAAc;IAElB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IArFa;AAwFN,MAAM,WAAW;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,2CAA2C;YAC3C,MAAM;iDAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,qBAAqB;wBACrB,MAAM,IAAI;6DAAQ,CAAA,UAAW,WAAW,SAAS;;wBAEjD,MAAM,YAAoB;4BACxB;gCACE,IAAI;gCACJ,OAAO;gCACP,MAAM;gCACN,OAAO;gCACP,UAAU;gCACV,QAAQ;oCAAC;oCAAsB;iCAAuB;gCACtD,UAAU;oCAAC;oCAAM;oCAAoB;oCAAQ;iCAAO;gCACpD,aAAa;gCACb,cAAc;gCACd,MAAM;gCACN,OAAO;gCACP,WAAW,EAAE;4BACf;4BACA;gCACE,IAAI;gCACJ,OAAO;gCACP,MAAM;gCACN,OAAO;gCACP,UAAU;gCACV,QAAQ;oCAAC;oCAAsB;iCAAuB;gCACtD,UAAU;oCAAC;oCAAM;oCAAoB;oCAAQ;iCAAU;gCACvD,aAAa;gCACb,cAAc;gCACd,MAAM;gCACN,OAAO;gCACP,WAAW,EAAE;4BACf;yBACD;wBAED,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,OAAO;QAAE;QAAO;QAAS;IAAM;AACjC;IAxDa;AA2DN,MAAM,kBAAkB;;IAC7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,yBAAyB;YACzB,MAAM,mBAAkC;gBACtC;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,MAAM;oBACN,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,MAAM;oBACN,UAAU;gBACZ;aACD;YAED,gBAAgB;QAClB;oCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,iBAAiB,aAAa,MAAM,IAAI,GAAG;YAEhD,MAAM,WAAW;sDAAY;oBAC3B;8DAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;gBAC1D;qDAAG;YAEH;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC7B,gBAAgB;YAChB,iBAAiB;YACjB,oCAAoC;YACpC;0DAAW,IAAM,iBAAiB;yDAAO;QAC3C;iDAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC5B;0DAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;QAC1D;iDAAG;QAAC,aAAa,MAAM;KAAC;IAExB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC5B;0DAAgB,CAAA,OAAQ,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;;QAChF;iDAAG;QAAC,aAAa,MAAM;KAAC;IAExB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAhEa;AAmEN,MAAM,gBAAgB;;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAE9E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;4DAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,IAAI,QAAQ,KAAK;wBACf,cAAc;oBAChB,OAAO,IAAI,QAAQ,MAAM;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG,EAAE;IAEL,OAAO;AACT;IAtBa;AAyBN,MAAM,kBAAkB,CAAI,KAAa;;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;oCAAK;YAChD;;YAEA,IAAI;gBACF,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;gBACzC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,mCAAsC,OAAJ,KAAI,OAAK;gBAC1D,OAAO;YACT;QACF;;IAEA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC5B,IAAI;gBACF,MAAM,eAAe,iBAAiB,WAAW,MAAM,eAAe;gBACtE,eAAe;gBAEf,wCAAmC;oBACjC,OAAO,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBAClD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,mCAAsC,OAAJ,KAAI,OAAK;YAC5D;QACF;gDAAG;QAAC;QAAK;KAAY;IAErB,OAAO;QAAC;QAAa;KAAS;AAChC;IA3Ba", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/sections/rooms.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Card } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON>dalContent, <PERSON>dalHeader, ModalTitle, ModalDescription } from \"@/components/ui/modal\"\nimport { ScrollAnimation, StaggeredAnimation } from \"@/components/ui/scroll-animation\"\nimport { useLazyImage } from \"@/utils/performance\"\nimport { useModal } from \"@/hooks\"\nimport { \n  Bed, \n  Bath, \n  Wifi, \n  Car, \n  Coffee, \n  Shield, \n  Users,\n  Square,\n  MapPin,\n  Star,\n  Eye,\n  Heart,\n  ArrowRight\n} from \"lucide-react\"\nimport type { Room } from \"@/types\"\n\n// Mock room data - replace with actual data\nconst mockRooms: Room[] = [\n  {\n    id: \"1\",\n    title: \"Deluxe Single Room\",\n    type: \"single\",\n    price: 1500000,\n    currency: \"IDR\",\n    images: [\n      \"/images/room-single-1.jpg\",\n      \"/images/room-single-2.jpg\",\n      \"/images/room-single-3.jpg\"\n    ],\n    features: [\"AC\", \"Private Bathroom\", \"WiFi\", \"Study Desk\", \"Wardrobe\"],\n    description: \"Comfortable single room perfect for students and young professionals. Features modern amenities and a peaceful environment for studying and rest.\",\n    availability: true,\n    size: 12,\n    floor: 2,\n    amenities: []\n  },\n  {\n    id: \"2\", \n    title: \"Premium Double Room\",\n    type: \"double\",\n    price: 2000000,\n    currency: \"IDR\",\n    images: [\n      \"/images/room-double-1.jpg\",\n      \"/images/room-double-2.jpg\", \n      \"/images/room-double-3.jpg\"\n    ],\n    features: [\"AC\", \"Private Bathroom\", \"WiFi\", \"Balcony\", \"Mini Fridge\"],\n    description: \"Spacious double room with balcony view. Ideal for sharing with a friend or for those who prefer extra space and comfort.\",\n    availability: true,\n    size: 18,\n    floor: 3,\n    amenities: []\n  },\n  {\n    id: \"3\",\n    title: \"Shared Economy Room\", \n    type: \"shared\",\n    price: 800000,\n    currency: \"IDR\",\n    images: [\n      \"/images/room-shared-1.jpg\",\n      \"/images/room-shared-2.jpg\",\n      \"/images/room-shared-3.jpg\"\n    ],\n    features: [\"AC\", \"Shared Bathroom\", \"WiFi\", \"Bunk Beds\", \"Lockers\"],\n    description: \"Budget-friendly shared accommodation with modern facilities. Perfect for students looking for affordable housing with a social atmosphere.\",\n    availability: true,\n    size: 15,\n    floor: 1,\n    amenities: []\n  },\n  {\n    id: \"4\",\n    title: \"Executive Suite\",\n    type: \"premium\", \n    price: 3500000,\n    currency: \"IDR\",\n    images: [\n      \"/images/room-suite-1.jpg\",\n      \"/images/room-suite-2.jpg\",\n      \"/images/room-suite-3.jpg\"\n    ],\n    features: [\"AC\", \"Private Bathroom\", \"WiFi\", \"Kitchenette\", \"Living Area\", \"Balcony\"],\n    description: \"Luxurious suite with separate living area and kitchenette. Perfect for professionals who value privacy and premium amenities.\",\n    availability: true,\n    size: 25,\n    floor: 4,\n    amenities: []\n  }\n]\n\ninterface RoomCardProps {\n  room: Room;\n  onViewDetails: (room: Room) => void;\n  delay?: number;\n}\n\nfunction RoomCard({ room, onViewDetails, delay = 0 }: RoomCardProps) {\n  const { targetRef, imageSrc, isLoaded } = useLazyImage(room.images[0], \"/images/placeholder-room.jpg\")\n  const [isFavorite, setIsFavorite] = React.useState(false)\n  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('id-ID', {\n      style: 'currency',\n      currency: 'IDR',\n      minimumFractionDigits: 0,\n    }).format(price)\n  }\n\n  const getRoomTypeColor = (type: string) => {\n    switch (type) {\n      case 'single': return 'bg-blue-100 text-blue-800'\n      case 'double': return 'bg-green-100 text-green-800'\n      case 'shared': return 'bg-orange-100 text-orange-800'\n      case 'premium': return 'bg-purple-100 text-purple-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <motion.div\n      ref={targetRef}\n      initial={{ opacity: 0, y: 30 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay }}\n      whileHover={{ y: -5 }}\n      className=\"group\"\n    >\n      <Card hover className=\"overflow-hidden h-full\">\n        {/* Image Section */}\n        <div className=\"relative h-48 overflow-hidden\">\n          {isLoaded ? (\n            <motion.img\n              src={imageSrc}\n              alt={room.title}\n              className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-500\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.3 }}\n            />\n          ) : (\n            <div className=\"w-full h-full bg-gray-200 animate-pulse flex items-center justify-center\">\n              <Bed className=\"h-8 w-8 text-gray-400\" />\n            </div>\n          )}\n          \n          {/* Overlay */}\n          <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300\" />\n          \n          {/* Room Type Badge */}\n          <div className=\"absolute top-3 left-3\">\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoomTypeColor(room.type)}`}>\n              {room.type.charAt(0).toUpperCase() + room.type.slice(1)}\n            </span>\n          </div>\n\n          {/* Favorite Button */}\n          <button\n            className=\"absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-full transition-all duration-200\"\n            onClick={(e) => {\n              e.stopPropagation()\n              setIsFavorite(!isFavorite)\n            }}\n            aria-label=\"Add to favorites\"\n          >\n            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />\n          </button>\n\n          {/* Availability Status */}\n          <div className=\"absolute bottom-3 left-3\">\n            <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${\n              room.availability \n                ? 'bg-green-100 text-green-800' \n                : 'bg-red-100 text-red-800'\n            }`}>\n              <div className={`w-2 h-2 rounded-full ${\n                room.availability ? 'bg-green-500' : 'bg-red-500'\n              }`} />\n              {room.availability ? 'Available' : 'Occupied'}\n            </div>\n          </div>\n        </div>\n\n        {/* Content Section */}\n        <div className=\"p-6 space-y-4\">\n          {/* Title and Price */}\n          <div className=\"space-y-2\">\n            <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\">\n              {room.title}\n            </h3>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-2xl font-bold text-blue-600\">\n                {formatPrice(room.price)}\n              </span>\n              <span className=\"text-sm text-gray-500\">/month</span>\n            </div>\n          </div>\n\n          {/* Room Info */}\n          <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n            <div className=\"flex items-center gap-1\">\n              <Square className=\"h-4 w-4\" />\n              <span>{room.size}m²</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <MapPin className=\"h-4 w-4\" />\n              <span>Floor {room.floor}</span>\n            </div>\n          </div>\n\n          {/* Features */}\n          <div className=\"space-y-2\">\n            <p className=\"text-sm font-medium text-gray-700\">Features:</p>\n            <div className=\"flex flex-wrap gap-2\">\n              {room.features.slice(0, 3).map((feature, index) => (\n                <span\n                  key={index}\n                  className=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md\"\n                >\n                  {feature}\n                </span>\n              ))}\n              {room.features.length > 3 && (\n                <span className=\"px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-md\">\n                  +{room.features.length - 3} more\n                </span>\n              )}\n            </div>\n          </div>\n\n          {/* Description */}\n          <p className=\"text-sm text-gray-600 line-clamp-2\">\n            {room.description}\n          </p>\n\n          {/* Action Button */}\n          <Button \n            className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n            onClick={() => onViewDetails(room)}\n          >\n            <Eye className=\"mr-2 h-4 w-4\" />\n            View Details\n          </Button>\n        </div>\n      </Card>\n    </motion.div>\n  )\n}\n\nexport function RoomsSection() {\n  const { isOpen, openModal, closeModal } = useModal()\n  const [selectedRoom, setSelectedRoom] = React.useState<Room | null>(null)\n\n  const handleViewDetails = (room: Room) => {\n    setSelectedRoom(room)\n    openModal()\n  }\n\n  return (\n    <section id=\"rooms\" className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <ScrollAnimation animation=\"fadeIn\" className=\"text-center mb-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"space-y-4\"\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\n              Choose Your Perfect Room\n            </h2>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              From budget-friendly shared spaces to premium suites, we have the perfect accommodation \n              to match your lifestyle and budget.\n            </p>\n          </motion.div>\n        </ScrollAnimation>\n\n        {/* Room Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {mockRooms.map((room, index) => (\n            <RoomCard\n              key={room.id}\n              room={room}\n              onViewDetails={handleViewDetails}\n              delay={index * 0.1}\n            />\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <ScrollAnimation animation=\"fadeIn\" className=\"text-center mt-12\">\n          <Button size=\"lg\" variant=\"outline\" className=\"border-blue-600 text-blue-600 hover:bg-blue-50\">\n            View All Rooms\n            <ArrowRight className=\"ml-2 h-5 w-5\" />\n          </Button>\n        </ScrollAnimation>\n      </div>\n\n      {/* Room Details Modal */}\n      {selectedRoom && (\n        <Modal\n          isOpen={isOpen}\n          onClose={closeModal}\n          title={selectedRoom.title}\n          size=\"lg\"\n        >\n          <ModalContent>\n            <div className=\"space-y-6\">\n              {/* Room Image */}\n              <div className=\"aspect-video rounded-lg overflow-hidden\">\n                <img\n                  src={selectedRoom.images[0]}\n                  alt={selectedRoom.title}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n\n              {/* Room Details */}\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold mb-2\">Room Information</h3>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Size:</span>\n                        <span className=\"font-medium\">{selectedRoom.size}m²</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Floor:</span>\n                        <span className=\"font-medium\">Floor {selectedRoom.floor}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Type:</span>\n                        <span className=\"font-medium capitalize\">{selectedRoom.type}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Monthly Rate:</span>\n                        <span className=\"font-bold text-blue-600\">\n                          {new Intl.NumberFormat('id-ID', {\n                            style: 'currency',\n                            currency: 'IDR',\n                            minimumFractionDigits: 0,\n                          }).format(selectedRoom.price)}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <h3 className=\"text-lg font-semibold mb-2\">Features</h3>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {selectedRoom.features.map((feature, index) => (\n                        <span\n                          key={index}\n                          className=\"px-3 py-1 bg-blue-50 text-blue-700 text-sm rounded-full\"\n                        >\n                          {feature}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold mb-2\">Description</h3>\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\n                      {selectedRoom.description}\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <Button className=\"w-full bg-blue-600 hover:bg-blue-700\">\n                      Book This Room\n                    </Button>\n                    <Button variant=\"outline\" className=\"w-full\">\n                      Schedule a Visit\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </ModalContent>\n        </Modal>\n      )}\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AA2BA,4CAA4C;AAC5C,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU;YAAC;YAAM;YAAoB;YAAQ;YAAc;SAAW;QACtE,aAAa;QACb,cAAc;QACd,MAAM;QACN,OAAO;QACP,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU;YAAC;YAAM;YAAoB;YAAQ;YAAW;SAAc;QACtE,aAAa;QACb,cAAc;QACd,MAAM;QACN,OAAO;QACP,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU;YAAC;YAAM;YAAmB;YAAQ;YAAa;SAAU;QACnE,aAAa;QACb,cAAc;QACd,MAAM;QACN,OAAO;QACP,WAAW,EAAE;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ;YACN;YACA;YACA;SACD;QACD,UAAU;YAAC;YAAM;YAAoB;YAAQ;YAAe;YAAe;SAAU;QACrF,aAAa;QACb,cAAc;QACd,MAAM;QACN,OAAO;QACP,WAAW,EAAE;IACf;CACD;AAQD,SAAS,SAAS,KAAiD;QAAjD,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,CAAC,EAAiB,GAAjD;;IAChB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEjE,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,WAAU;kBAEV,cAAA,6LAAC,4HAAA,CAAA,OAAI;YAAC,KAAK;YAAC,WAAU;;8BAEpB,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK;4BACL,KAAK,KAAK,KAAK;4BACf,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;4BAAI;;;;;iDAG9B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAW,AAAC,8CAAyE,OAA5B,iBAAiB,KAAK,IAAI;0CACtF,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;sCAKzD,6LAAC;4BACC,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,cAAc,CAAC;4BACjB;4BACA,cAAW;sCAEX,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAW,AAAC,WAAqE,OAA3D,aAAa,8BAA8B;;;;;;;;;;;sCAI1E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,AAAC,sEAIhB,OAHC,KAAK,YAAY,GACb,gCACA;;kDAEJ,6LAAC;wCAAI,WAAW,AAAC,wBAEhB,OADC,KAAK,YAAY,GAAG,iBAAiB;;;;;;oCAEtC,KAAK,YAAY,GAAG,cAAc;;;;;;;;;;;;;;;;;;8BAMzC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,YAAY,KAAK,KAAK;;;;;;sDAEzB,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;;gDAAM,KAAK,IAAI;gDAAC;;;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;;gDAAK;gDAAO,KAAK,KAAK;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACvC,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACtB,6LAAC;4CAAK,WAAU;;gDAAwD;gDACpE,KAAK,QAAQ,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;sCAOnC,6LAAC;4BAAE,WAAU;sCACV,KAAK,WAAW;;;;;;sCAInB,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;;8CAE7B,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GAvJS;;QACmC,uHAAA,CAAA,eAAY;;;KAD/C;AAyJF,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,WAAc,CAAc;IAEpE,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;;0BAC5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,2IAAA,CAAA,kBAAe;wBAAC,WAAU;wBAAS,WAAU;kCAC5C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gCAEC,MAAM;gCACN,eAAe;gCACf,OAAO,QAAQ;+BAHV,KAAK,EAAE;;;;;;;;;;kCASlB,6LAAC,2IAAA,CAAA,kBAAe;wBAAC,WAAU;wBAAS,WAAU;kCAC5C,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,SAAQ;4BAAU,WAAU;;gCAAiD;8CAE7F,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM3B,8BACC,6LAAC,6HAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS;gBACT,OAAO,aAAa,KAAK;gBACzB,MAAK;0BAEL,cAAA,6LAAC,6HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK,aAAa,MAAM,CAAC,EAAE;oCAC3B,KAAK,aAAa,KAAK;oCACvB,WAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EAAe,aAAa,IAAI;4EAAC;;;;;;;;;;;;;0EAEnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EAAc;4EAAO,aAAa,KAAK;;;;;;;;;;;;;0EAEzD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAA0B,aAAa,IAAI;;;;;;;;;;;;0EAE7D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,YAAY,CAAC,SAAS;4EAC9B,OAAO;4EACP,UAAU;4EACV,uBAAuB;wEACzB,GAAG,MAAM,CAAC,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6LAAC;wDAAI,WAAU;kEACZ,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;;;;;;;;;;;;;kDAUf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6LAAC;wDAAE,WAAU;kEACV,aAAa,WAAW;;;;;;;;;;;;0DAI7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDAAC,WAAU;kEAAuC;;;;;;kEAGzD,6LAAC,8HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjE;IA5IgB;;QAC4B,iHAAA,CAAA,WAAQ;;;MADpC", "debugId": null}}]}