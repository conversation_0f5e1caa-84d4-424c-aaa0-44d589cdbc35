"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>dalContent, ModalHeader, ModalTitle, ModalDescription } from "@/components/ui/modal"
import { ScrollAnimation, StaggeredAnimation } from "@/components/ui/scroll-animation"
import { useLazyImage } from "@/utils/performance"
import { useModal } from "@/hooks"
import { getKostImage, FALLBACK_IMAGES } from "@/lib/images"
import { 
  Bed, 
  Bath, 
  Wifi, 
  Car, 
  Coffee, 
  Shield, 
  Users,
  Square,
  MapPin,
  Star,
  Eye,
  Heart,
  ArrowRight
} from "lucide-react"
import type { Room } from "@/types"

// Mock room data - replace with actual data
const mockRooms: Room[] = [
  {
    id: "1",
    title: "Deluxe Single Room",
    type: "single",
    price: 1500000,
    currency: "IDR",
    images: [
      getKostImage(0),
      getKostImage(1),
      getKostImage(2)
    ],
    features: ["AC", "Private Bathroom", "WiFi", "Study Desk", "Wardrobe"],
    description: "Comfortable single room perfect for students and young professionals. Features modern amenities and a peaceful environment for studying and rest.",
    availability: true,
    size: 12,
    floor: 2,
    amenities: []
  },
  {
    id: "2", 
    title: "Premium Double Room",
    type: "double",
    price: 2000000,
    currency: "IDR",
    images: [
      getKostImage(3),
      getKostImage(4),
      getKostImage(5)
    ],
    features: ["AC", "Private Bathroom", "WiFi", "Balcony", "Mini Fridge"],
    description: "Spacious double room with balcony view. Ideal for sharing with a friend or for those who prefer extra space and comfort.",
    availability: true,
    size: 18,
    floor: 3,
    amenities: []
  },
  {
    id: "3",
    title: "Shared Economy Room", 
    type: "shared",
    price: 800000,
    currency: "IDR",
    images: [
      getKostImage(6),
      getKostImage(7),
      getKostImage(8)
    ],
    features: ["AC", "Shared Bathroom", "WiFi", "Bunk Beds", "Lockers"],
    description: "Budget-friendly shared accommodation with modern facilities. Perfect for students looking for affordable housing with a social atmosphere.",
    availability: true,
    size: 15,
    floor: 1,
    amenities: []
  },
  {
    id: "4",
    title: "Executive Suite",
    type: "premium", 
    price: 3500000,
    currency: "IDR",
    images: [
      getKostImage(9),
      getKostImage(10),
      getKostImage(11)
    ],
    features: ["AC", "Private Bathroom", "WiFi", "Kitchenette", "Living Area", "Balcony"],
    description: "Luxurious suite with separate living area and kitchenette. Perfect for professionals who value privacy and premium amenities.",
    availability: true,
    size: 25,
    floor: 4,
    amenities: []
  }
]

interface RoomCardProps {
  room: Room;
  onViewDetails: (room: Room) => void;
  delay?: number;
}

function RoomCard({ room, onViewDetails, delay = 0 }: RoomCardProps) {
  const { targetRef, imageSrc, isLoaded } = useLazyImage(room.images[0], FALLBACK_IMAGES.kost)
  const [isFavorite, setIsFavorite] = React.useState(false)
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getRoomTypeColor = (type: string) => {
    switch (type) {
      case 'single': return 'bg-blue-100 text-blue-800'
      case 'double': return 'bg-green-100 text-green-800'
      case 'shared': return 'bg-orange-100 text-orange-800'
      case 'premium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <motion.div
      ref={targetRef}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      whileHover={{ y: -5 }}
      className="group"
    >
      <Card hover className="overflow-hidden h-full">
        {/* Image Section */}
        <div className="relative h-48 overflow-hidden">
          {isLoaded ? (
            <motion.img
              src={imageSrc}
              alt={room.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
              <Bed className="h-8 w-8 text-gray-400" />
            </div>
          )}
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300" />
          
          {/* Room Type Badge */}
          <div className="absolute top-3 left-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoomTypeColor(room.type)}`}>
              {room.type.charAt(0).toUpperCase() + room.type.slice(1)}
            </span>
          </div>

          {/* Favorite Button */}
          <button
            className="absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-full transition-all duration-200"
            onClick={(e) => {
              e.stopPropagation()
              setIsFavorite(!isFavorite)
            }}
            aria-label="Add to favorites"
          >
            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
          </button>

          {/* Availability Status */}
          <div className="absolute bottom-3 left-3">
            <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
              room.availability 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                room.availability ? 'bg-green-500' : 'bg-red-500'
              }`} />
              {room.availability ? 'Available' : 'Occupied'}
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6 space-y-4">
          {/* Title and Price */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {room.title}
            </h3>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-blue-600">
                {formatPrice(room.price)}
              </span>
              <span className="text-sm text-gray-500">/month</span>
            </div>
          </div>

          {/* Room Info */}
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Square className="h-4 w-4" />
              <span>{room.size}m²</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              <span>Floor {room.floor}</span>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Features:</p>
            <div className="flex flex-wrap gap-2">
              {room.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                >
                  {feature}
                </span>
              ))}
              {room.features.length > 3 && (
                <span className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-md">
                  +{room.features.length - 3} more
                </span>
              )}
            </div>
          </div>

          {/* Description */}
          <p className="text-sm text-gray-600 line-clamp-2">
            {room.description}
          </p>

          {/* Action Button */}
          <Button 
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => onViewDetails(room)}
          >
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Button>
        </div>
      </Card>
    </motion.div>
  )
}

export function RoomsSection() {
  const { isOpen, openModal, closeModal } = useModal()
  const [selectedRoom, setSelectedRoom] = React.useState<Room | null>(null)

  const handleViewDetails = (room: Room) => {
    setSelectedRoom(room)
    openModal()
  }

  return (
    <section id="rooms" className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <ScrollAnimation animation="fadeIn" className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-4"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Choose Your Perfect Room
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From budget-friendly shared spaces to premium suites, we have the perfect accommodation 
              to match your lifestyle and budget.
            </p>
          </motion.div>
        </ScrollAnimation>

        {/* Room Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {mockRooms.map((room, index) => (
            <RoomCard
              key={room.id}
              room={room}
              onViewDetails={handleViewDetails}
              delay={index * 0.1}
            />
          ))}
        </div>

        {/* View All Button */}
        <ScrollAnimation animation="fadeIn" className="text-center mt-12">
          <Button size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
            View All Rooms
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </ScrollAnimation>
      </div>

      {/* Room Details Modal */}
      {selectedRoom && (
        <Modal
          isOpen={isOpen}
          onClose={closeModal}
          title={selectedRoom.title}
          size="lg"
        >
          <ModalContent>
            <div className="space-y-6">
              {/* Room Image */}
              <div className="aspect-video rounded-lg overflow-hidden">
                <img
                  src={selectedRoom.images[0]}
                  alt={selectedRoom.title}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Room Details */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Room Information</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Size:</span>
                        <span className="font-medium">{selectedRoom.size}m²</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Floor:</span>
                        <span className="font-medium">Floor {selectedRoom.floor}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Type:</span>
                        <span className="font-medium capitalize">{selectedRoom.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Monthly Rate:</span>
                        <span className="font-bold text-blue-600">
                          {new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            minimumFractionDigits: 0,
                          }).format(selectedRoom.price)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-2">Features</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedRoom.features.map((feature, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-50 text-blue-700 text-sm rounded-full"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Description</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {selectedRoom.description}
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Book This Room
                    </Button>
                    <Button variant="outline" className="w-full">
                      Schedule a Visit
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </ModalContent>
        </Modal>
      )}
    </section>
  )
}
