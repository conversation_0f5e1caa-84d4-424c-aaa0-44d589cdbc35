"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollAnimation, StaggeredAnimation, ParallaxScroll } from "@/components/ui/scroll-animation"
import { UNSPLASH_IMAGES } from "@/lib/images"
import {
  ArrowRight,
  Play,
  Star,
  Users,
  Shield,
  Wifi,
  MapPin,
  Phone
} from "lucide-react"

const heroStats = [
  {
    icon: Users,
    value: "200+",
    label: "Happy Residents"
  },
  {
    icon: Star,
    value: "4.9",
    label: "Average Rating"
  },
  {
    icon: Shield,
    value: "24/7",
    label: "Security"
  },
  {
    icon: Wifi,
    value: "100%",
    label: "High-Speed WiFi"
  }
]

const heroFeatures = [
  "Modern & Clean Rooms",
  "24/7 Security System", 
  "High-Speed Internet",
  "Shared Kitchen & Lounge",
  "Laundry Facilities",
  "Strategic Location"
]

export function HeroSection() {
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)
  
  // Hero images from Unsplash
  const heroImages = [
    UNSPLASH_IMAGES.hero.hero1,
    UNSPLASH_IMAGES.hero.hero2,
    UNSPLASH_IMAGES.hero.hero3
  ]

  // Auto-rotate hero images
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [heroImages.length])

  const handleScrollToRooms = () => {
    const roomsSection = document.getElementById('rooms')
    if (roomsSection) {
      roomsSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handleScrollToContact = () => {
    const contactSection = document.getElementById('contact')
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image Slider */}
      <div className="absolute inset-0 z-0">
        {heroImages.map((image, index) => (
          <motion.div
            key={index}
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url(${image})`
            }}
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: index === currentImageIndex ? 1 : 0,
              scale: index === currentImageIndex ? 1.05 : 1
            }}
            transition={{ duration: 1, ease: "easeInOut" }}
          />
        ))}
      </div>

      {/* Parallax Background Elements */}
      <ParallaxScroll offset={30} className="absolute inset-0 z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-blue-400/10 rounded-full blur-3xl" />
      </ParallaxScroll>

      {/* Main Content */}
      <div className="relative z-20 container mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Content */}
          <div className="text-center lg:text-left space-y-8">
            <StaggeredAnimation staggerDelay={0.2}>
              {/* Badge */}
              <motion.div
                className="inline-flex items-center gap-2 bg-blue-600/20 backdrop-blur-sm border border-blue-400/30 rounded-full px-4 py-2 text-blue-100"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <MapPin className="h-4 w-4" />
                <span className="text-sm font-medium">Prime Location in Jakarta</span>
              </motion.div>

              {/* Main Headline */}
              <motion.h1 
                className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Find Your Ideal
                <br />
                <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent">
                  Boarding House
                </span>
                <br />
                Here!
              </motion.h1>

              {/* Subtitle */}
              <motion.p 
                className="text-lg sm:text-xl text-gray-200 max-w-2xl mx-auto lg:mx-0 leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Experience comfortable living with modern amenities, 24/7 security, and a vibrant community. 
                Perfect for students and young professionals.
              </motion.p>

              {/* Features List */}
              <motion.div 
                className="grid grid-cols-2 gap-3 max-w-md mx-auto lg:mx-0"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                {heroFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-gray-200">
                    <div className="w-2 h-2 bg-blue-400 rounded-full" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </motion.div>

              {/* CTA Buttons */}
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 pt-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                <Button 
                  size="lg" 
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                  onClick={handleScrollToRooms}
                >
                  View Rooms
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-3 text-lg font-semibold"
                  onClick={handleScrollToContact}
                >
                  <Phone className="mr-2 h-5 w-5" />
                  Contact Us
                </Button>
              </motion.div>
            </StaggeredAnimation>
          </div>

          {/* Right Content - Stats */}
          <div className="lg:justify-self-end">
            <motion.div 
              className="grid grid-cols-2 gap-6 max-w-md mx-auto"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              {heroStats.map((stat, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 backdrop-blur-md rounded-2xl p-6 text-center border border-white/20 hover:bg-white/20 transition-all duration-300"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="flex flex-col items-center space-y-3">
                    <div className="p-3 bg-blue-500/20 rounded-full">
                      <stat.icon className="h-6 w-6 text-blue-400" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-white">
                        {stat.value}
                      </div>
                      <div className="text-sm text-gray-300 font-medium">
                        {stat.label}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1.2 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-white/70 rounded-full mt-2"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>

      {/* Image Indicators */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
        {heroImages.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentImageIndex 
                ? 'bg-blue-400 scale-125' 
                : 'bg-white/50 hover:bg-white/70'
            }`}
            onClick={() => setCurrentImageIndex(index)}
            aria-label={`View image ${index + 1}`}
          />
        ))}
      </div>
    </section>
  )
}
