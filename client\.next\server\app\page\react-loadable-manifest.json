{"[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/_2e07f44f._.js", "static/chunks/components_sections_testimonials_tsx_a3096555._.js"]}, "[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/_a52c4a65._.js", "static/chunks/components_sections_facilities_tsx_a3096555._.js"]}, "[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/_f4bd1bf7._.js", "static/chunks/components_sections_hero_tsx_a3096555._.js"]}, "[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/_9920074e._.js", "static/chunks/node_modules_lucide-react_dist_esm_icons_fb8c4428._.js", "static/chunks/components_sections_rooms_tsx_a3096555._.js"]}}