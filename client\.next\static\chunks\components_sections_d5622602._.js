(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_1157edb3._.js",
  "static/chunks/components_sections_hero_tsx_1b32b315._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/hero.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_7bb19f53._.js",
  "static/chunks/node_modules_3cf66a11._.js",
  "static/chunks/components_sections_rooms_tsx_1b32b315._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/rooms.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_273cbcb6._.js",
  "static/chunks/components_sections_facilities_tsx_1b32b315._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/facilities.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_16eb6837._.js",
  "static/chunks/components_sections_testimonials_tsx_1b32b315._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/testimonials.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);