module.exports = {

"[project]/components/sections/hero.tsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_00c18291._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/sections/hero.tsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}),

};