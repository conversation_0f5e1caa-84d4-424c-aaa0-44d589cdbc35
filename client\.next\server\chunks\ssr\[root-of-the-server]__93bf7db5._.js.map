{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/app/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\"\r\nimport { BoardingHouseLanding, BoardingHouseErrorBoundary } from \"@/components/boarding-house-landing\"\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"BoardingHouse - Find Your Ideal Boarding House\",\r\n  description: \"Experience comfortable living with modern amenities, 24/7 security, and a vibrant community. Perfect for students and young professionals in Jakarta.\",\r\n  keywords: [\"boarding house\", \"kost\", \"accommodation\", \"Jakarta\", \"student housing\", \"modern facilities\"],\r\n  authors: [{ name: \"BoardingHouse Team\" }],\r\n  openGraph: {\r\n    title: \"BoardingHouse - Find Your Ideal Boarding House\",\r\n    description: \"Experience comfortable living with modern amenities, 24/7 security, and a vibrant community.\",\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n    siteName: \"BoardingHouse\",\r\n  },\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: \"BoardingHouse - Find Your Ideal Boarding House\",\r\n    description: \"Experience comfortable living with modern amenities, 24/7 security, and a vibrant community.\",\r\n  },\r\n  robots: {\r\n    index: true,\r\n    follow: true,\r\n    googleBot: {\r\n      index: true,\r\n      follow: true,\r\n      \"max-video-preview\": -1,\r\n      \"max-image-preview\": \"large\",\r\n      \"max-snippet\": -1,\r\n    },\r\n  },\r\n  verification: {\r\n    google: \"your-google-verification-code\",\r\n  },\r\n}\r\n\r\n// Mock data untuk featured kost\r\nconst featuredKosts: KostData[] = [\r\n  {\r\n    id: \"1\",\r\n    title: \"Kost Melati Residence\",\r\n    location: \"Kemang, Jakarta Selatan\",\r\n    price: 2500000,\r\n    rating: 4.8,\r\n    reviewCount: 124,\r\n    images: [\r\n      UNSPLASH_IMAGES.kost.room1,\r\n      UNSPLASH_IMAGES.kost.interior1,\r\n      UNSPLASH_IMAGES.kost.interior2\r\n    ],\r\n    facilities: [\"WiFi\", \"Parkir\", \"Dapur\", \"Listrik\", \"Air\", \"Keamanan\"],\r\n    type: \"putri\",\r\n    available: 3,\r\n    description: \"Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.\",\r\n    isWishlisted: false\r\n  },\r\n  {\r\n    id: \"2\",\r\n    title: \"Griya Mahasiswa Bandung\",\r\n    location: \"Dago, Bandung\",\r\n    price: 1800000,\r\n    rating: 4.6,\r\n    reviewCount: 89,\r\n    images: [\r\n      UNSPLASH_IMAGES.kost.room2,\r\n      UNSPLASH_IMAGES.kost.interior3\r\n    ],\r\n    facilities: [\"WiFi\", \"Dapur\", \"Listrik\", \"Air\", \"Ruang Tamu\"],\r\n    type: \"putra\",\r\n    available: 5,\r\n    description: \"Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.\",\r\n    isWishlisted: true\r\n  },\r\n  {\r\n    id: \"3\",\r\n    title: \"Kost Harmoni Yogya\",\r\n    location: \"Malioboro, Yogyakarta\",\r\n    price: 1500000,\r\n    rating: 4.7,\r\n    reviewCount: 156,\r\n    images: [UNSPLASH_IMAGES.kost.room3],\r\n    facilities: [\"WiFi\", \"Parkir\", \"Listrik\", \"Air\", \"Keamanan\", \"AC\"],\r\n    type: \"campur\",\r\n    available: 2,\r\n    description: \"Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.\",\r\n    isWishlisted: false\r\n  }\r\n]\r\n\r\nconst testimonials = [\r\n  {\r\n    name: \"Sarah Putri\",\r\n    location: \"Jakarta\",\r\n    rating: 5,\r\n    comment: \"Platform yang sangat membantu! Fitur perbandingan kostnya sangat berguna untuk memilih kost yang tepat.\",\r\n    avatar: UNSPLASH_IMAGES.avatars.female1\r\n  },\r\n  {\r\n    name: \"Ahmad Rizki\",\r\n    location: \"Bandung\",\r\n    rating: 5,\r\n    comment: \"Preview dinamis membuat saya bisa melihat detail kost dengan jelas sebelum memutuskan. Recommended!\",\r\n    avatar: UNSPLASH_IMAGES.avatars.male1\r\n  },\r\n  {\r\n    name: \"Dina Maharani\",\r\n    location: \"Yogyakarta\",\r\n    rating: 4,\r\n    comment: \"Pencarian kost jadi lebih mudah dan cepat. Interface yang user-friendly dan informatif.\",\r\n    avatar: UNSPLASH_IMAGES.avatars.female2\r\n  }\r\n]\r\n\r\nexport default function Home() {\r\n  const [selectedKost, setSelectedKost] = useState<KostData | null>(null)\r\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false)\r\n  const [comparisonKosts, setComparisonKosts] = useState<KostData[]>([])\r\n  const [isComparisonOpen, setIsComparisonOpen] = useState(false)\r\n  const [wishlistedKosts, setWishlistedKosts] = useState<string[]>([\"2\"])\r\n\r\n  const handleSearch = (query: string, filters: SearchFilters) => {\r\n    // TODO: Implement search functionality\r\n    console.log(\"Search:\", query, filters)\r\n    // Navigate to listings page or filter results\r\n  }\r\n\r\n  const handlePreview = (kost: KostData) => {\r\n    setSelectedKost(kost)\r\n    setIsPreviewOpen(true)\r\n  }\r\n\r\n  const handleWishlist = (kostId: string) => {\r\n    setWishlistedKosts(prev =>\r\n      prev.includes(kostId)\r\n        ? prev.filter(id => id !== kostId)\r\n        : [...prev, kostId]\r\n    )\r\n  }\r\n\r\n  const handleCompare = (kostId: string) => {\r\n    const kost = featuredKosts.find(k => k.id === kostId)\r\n    if (!kost) return\r\n\r\n    setComparisonKosts(prev => {\r\n      const isAlreadyComparing = prev.some(k => k.id === kostId)\r\n      if (isAlreadyComparing) {\r\n        return prev.filter(k => k.id !== kostId)\r\n      } else if (prev.length < 3) {\r\n        return [...prev, kost]\r\n      } else {\r\n        // Replace the first item if already at max\r\n        return [kost, ...prev.slice(1)]\r\n      }\r\n    })\r\n  }\r\n\r\n  const removeFromComparison = (kostId: string) => {\r\n    setComparisonKosts(prev => prev.filter(k => k.id !== kostId))\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Hero Section */}\r\n      <HeroSection onSearch={handleSearch} />\r\n\r\n      {/* Featured Kosts Section */}\r\n      <section id=\"kost-listings\" className=\"py-16 bg-background\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"text-center mb-12\">\r\n            <Badge variant=\"outline\" className=\"mb-4\">\r\n              <TrendingUp className=\"h-4 w-4 mr-2\" />\r\n              Kost Terpopuler\r\n            </Badge>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\r\n              Kost Pilihan Terbaik\r\n            </h2>\r\n            <p className=\"text-muted-foreground text-lg max-w-2xl mx-auto\">\r\n              Temukan kost berkualitas tinggi yang telah dipilih khusus untuk Anda\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\r\n            {featuredKosts.map((kost) => (\r\n              <KostCard\r\n                key={kost.id}\r\n                kost={{\r\n                  ...kost,\r\n                  isWishlisted: wishlistedKosts.includes(kost.id)\r\n                }}\r\n                onPreview={handlePreview}\r\n                onWishlist={handleWishlist}\r\n                onCompare={handleCompare}\r\n                isComparing={comparisonKosts.some(k => k.id === kost.id)}\r\n              />\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"text-center\">\r\n            <Button size=\"lg\" variant=\"outline\">\r\n              Lihat Semua Kost\r\n              <ArrowRight className=\"h-4 w-4 ml-2\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <Separator />\r\n\r\n      {/* Testimonials Section */}\r\n      <section className=\"py-16 bg-muted/30\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"text-center mb-12\">\r\n            <Badge variant=\"outline\" className=\"mb-4\">\r\n              <Users className=\"h-4 w-4 mr-2\" />\r\n              Testimoni Pengguna\r\n            </Badge>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\r\n              Apa Kata Mereka?\r\n            </h2>\r\n            <p className=\"text-muted-foreground text-lg max-w-2xl mx-auto\">\r\n              Pengalaman nyata dari pengguna KostHub yang telah menemukan kost impian mereka\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-3 gap-6\">\r\n            {testimonials.map((testimonial, index) => (\r\n              <div key={index} className=\"bg-card p-6 rounded-lg border\">\r\n                <div className=\"flex items-center gap-1 mb-4\">\r\n                  {Array.from({ length: testimonial.rating }).map((_, i) => (\r\n                    <Star key={i} className=\"h-4 w-4 fill-yellow-400 text-yellow-400\" />\r\n                  ))}\r\n                </div>\r\n                <p className=\"text-muted-foreground mb-4 leading-relaxed\">\r\n                  \"{testimonial.comment}\"\r\n                </p>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"relative w-10 h-10 rounded-full overflow-hidden\">\r\n                    <Image\r\n                      src={testimonial.avatar}\r\n                      alt={`${testimonial.name} avatar`}\r\n                      fill\r\n                      className=\"object-cover\"\r\n                      sizes=\"40px\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"font-medium\">{testimonial.name}</div>\r\n                    <div className=\"text-sm text-muted-foreground flex items-center gap-1\">\r\n                      <MapPin className=\"h-3 w-3\" />\r\n                      {testimonial.location}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <Separator />\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-background\">\r\n        <div className=\"container mx-auto px-4 text-center\">\r\n          <div className=\"max-w-3xl mx-auto space-y-6\">\r\n            <Badge variant=\"outline\" className=\"mb-4\">\r\n              <Shield className=\"h-4 w-4 mr-2\" />\r\n              Bergabung Sekarang\r\n            </Badge>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold\">\r\n              Siap Menemukan Kost Impian Anda?\r\n            </h2>\r\n            <p className=\"text-muted-foreground text-lg\">\r\n              Bergabunglah dengan ribuan pengguna yang telah menemukan tempat tinggal ideal mereka melalui KostHub\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n              <Button size=\"lg\" className=\"px-8\">\r\n                <Heart className=\"h-4 w-4 mr-2\" />\r\n                Mulai Pencarian\r\n              </Button>\r\n              <Button size=\"lg\" variant=\"outline\" className=\"px-8\">\r\n                Daftarkan Kost Anda\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Comparison Floating Button */}\r\n      {comparisonKosts.length > 0 && (\r\n        <div className=\"fixed bottom-6 right-6 z-50\">\r\n          <Button\r\n            onClick={() => setIsComparisonOpen(true)}\r\n            className=\"rounded-full shadow-lg\"\r\n            size=\"lg\"\r\n          >\r\n            Bandingkan ({comparisonKosts.length})\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Dialogs */}\r\n      <Suspense fallback={<DialogSkeleton />}>\r\n        <KostPreviewDialog\r\n          kost={selectedKost}\r\n          isOpen={isPreviewOpen}\r\n          onClose={() => setIsPreviewOpen(false)}\r\n          onWishlist={handleWishlist}\r\n          onCompare={handleCompare}\r\n          isComparing={selectedKost ? comparisonKosts.some(k => k.id === selectedKost.id) : false}\r\n        />\r\n\r\n        <ComparisonDialog\r\n          kosts={comparisonKosts}\r\n          isOpen={isComparisonOpen}\r\n          onClose={() => setIsComparisonOpen(false)}\r\n          onRemoveFromComparison={removeFromComparison}\r\n        />\r\n      </Suspense>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAkB;QAAQ;QAAiB;QAAW;QAAmB;KAAoB;IACxG,SAAS;QAAC;YAAE,MAAM;QAAqB;KAAE;IACzC,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IACV;AACF;AAEA,gCAAgC;AAChC,MAAM,gBAA4B;IAChC;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;YACN,gBAAgB,IAAI,CAAC,KAAK;YAC1B,gBAAgB,IAAI,CAAC,SAAS;YAC9B,gBAAgB,IAAI,CAAC,SAAS;SAC/B;QACD,YAAY;YAAC;YAAQ;YAAU;YAAS;YAAW;YAAO;SAAW;QACrE,MAAM;QACN,WAAW;QACX,aAAa;QACb,cAAc;IAChB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;YACN,gBAAgB,IAAI,CAAC,KAAK;YAC1B,gBAAgB,IAAI,CAAC,SAAS;SAC/B;QACD,YAAY;YAAC;YAAQ;YAAS;YAAW;YAAO;SAAa;QAC7D,MAAM;QACN,WAAW;QACX,aAAa;QACb,cAAc;IAChB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;YAAC,gBAAgB,IAAI,CAAC,KAAK;SAAC;QACpC,YAAY;YAAC;YAAQ;YAAU;YAAW;YAAO;YAAY;SAAK;QAClE,MAAM;QACN,WAAW;QACX,aAAa;QACb,cAAc;IAChB;CACD;AAED,MAAM,eAAe;IACnB;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,SAAS;QACT,QAAQ,gBAAgB,OAAO,CAAC,OAAO;IACzC;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,SAAS;QACT,QAAQ,gBAAgB,OAAO,CAAC,KAAK;IACvC;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,SAAS;QACT,QAAQ,gBAAgB,OAAO,CAAC,OAAO;IACzC;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,SAA0B;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,SAAS;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,SAAqB,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,SAAS;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,SAAmB;QAAC;KAAI;IAEtE,MAAM,eAAe,CAAC,OAAe;QACnC,uCAAuC;QACvC,QAAQ,GAAG,CAAC,WAAW,OAAO;IAC9B,8CAA8C;IAChD;IAEA,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,IAAI,CAAC,MAAM;QAEX,mBAAmB,CAAA;YACjB,MAAM,qBAAqB,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACnD,IAAI,oBAAoB;gBACtB,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACnC,OAAO,IAAI,KAAK,MAAM,GAAG,GAAG;gBAC1B,OAAO;uBAAI;oBAAM;iBAAK;YACxB,OAAO;gBACL,2CAA2C;gBAC3C,OAAO;oBAAC;uBAAS,KAAK,KAAK,CAAC;iBAAG;YACjC;QACF;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACvD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAY,UAAU;;;;;;0BAGvB,8OAAC;gBAAQ,IAAG;gBAAgB,WAAU;0BACpC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC;4CAAW,WAAU;;;;;;wCAAiB;;;;;;;8CAGzC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAEC,MAAM;wCACJ,GAAG,IAAI;wCACP,cAAc,gBAAgB,QAAQ,CAAC,KAAK,EAAE;oCAChD;oCACA,WAAW;oCACX,YAAY;oCACZ,WAAW;oCACX,aAAa,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;mCARlD,KAAK,EAAE;;;;;;;;;;sCAalB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,MAAK;gCAAK,SAAQ;;oCAAU;kDAElC,8OAAC;wCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC;;;;;0BAGD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC;4CAAM,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC;gDAAE,QAAQ,YAAY,MAAM;4CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClD,8OAAC;oDAAa,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;;gDAA6C;gDACtD,YAAY,OAAO;gDAAC;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAK,YAAY,MAAM;wDACvB,KAAK,GAAG,YAAY,IAAI,CAAC,OAAO,CAAC;wDACjC,IAAI;wDACJ,WAAU;wDACV,OAAM;;;;;;;;;;;8DAGV,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAe,YAAY,IAAI;;;;;;sEAC9C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAO,WAAU;;;;;;gEACjB,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;mCAvBnB;;;;;;;;;;;;;;;;;;;;;0BAiClB,8OAAC;;;;;0BAGD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC;wCAAO,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAG/C,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAG7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,MAAK;wCAAK,WAAU;;0DAC1B,8OAAC;gDAAM,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAO,MAAK;wCAAK,SAAQ;wCAAU,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS5D,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS,IAAM,oBAAoB;oBACnC,WAAU;oBACV,MAAK;;wBACN;wBACc,gBAAgB,MAAM;wBAAC;;;;;;;;;;;;0BAM1C,8OAAC;gBAAS,wBAAU,8OAAC;;;;;;kCACnB,8OAAC;wBACC,MAAM;wBACN,QAAQ;wBACR,SAAS,IAAM,iBAAiB;wBAChC,YAAY;wBACZ,WAAW;wBACX,aAAa,eAAe,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE,IAAI;;;;;;kCAGpF,8OAAC;wBACC,OAAO;wBACP,QAAQ;wBACR,SAAS,IAAM,oBAAoB;wBACnC,wBAAwB;;;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}]}