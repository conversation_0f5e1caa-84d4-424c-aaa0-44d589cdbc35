(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/kost-preview-dialog.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_0ccda33c._.js",
  "static/chunks/components_kost-preview-dialog_tsx_bd45d73d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/kost-preview-dialog.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/components/comparison-dialog.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_918613f2._.js",
  "static/chunks/components_comparison-dialog_tsx_bd45d73d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/comparison-dialog.tsx [app-client] (ecmascript)");
    });
});
}),
}]);