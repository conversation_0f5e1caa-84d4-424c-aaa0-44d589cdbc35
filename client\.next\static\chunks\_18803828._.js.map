{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/ui/loading.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { spinnerVariants } from \"@/utils/animations\"\n\nconst loadingVariants = cva(\n  \"flex items-center justify-center\",\n  {\n    variants: {\n      variant: {\n        spinner: \"\",\n        skeleton: \"animate-pulse bg-muted rounded\",\n        dots: \"space-x-1\",\n      },\n      size: {\n        sm: \"h-4 w-4\",\n        md: \"h-6 w-6\",\n        lg: \"h-8 w-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"spinner\",\n      size: \"md\",\n    },\n  }\n)\n\ninterface LoadingProps extends VariantProps<typeof loadingVariants> {\n  text?: string;\n  className?: string;\n}\n\n// Spinner Loading Component\nfunction SpinnerLoading({ size, className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  const sizeClasses = {\n    sm: \"h-4 w-4 border-2\",\n    md: \"h-6 w-6 border-2\",\n    lg: \"h-8 w-8 border-3\",\n  };\n\n  return (\n    <motion.div\n      className={cn(\n        \"rounded-full border-current border-t-transparent\",\n        sizeClasses[size || \"md\"],\n        className\n      )}\n      variants={spinnerVariants}\n      animate=\"animate\"\n      aria-hidden=\"true\"\n    />\n  );\n}\n\n// Skeleton Loading Component\nfunction SkeletonLoading({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  );\n}\n\n// Dots Loading Component\nfunction DotsLoading({ size, className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  const dotSizes = {\n    sm: \"h-1 w-1\",\n    md: \"h-2 w-2\",\n    lg: \"h-3 w-3\",\n  };\n\n  const dotSize = dotSizes[size || \"md\"];\n\n  return (\n    <div className={cn(\"flex space-x-1\", className)}>\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className={cn(\"bg-current rounded-full\", dotSize)}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 0.6,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n          aria-hidden=\"true\"\n        />\n      ))}\n    </div>\n  );\n}\n\n// Main Loading Component\nexport function Loading({ variant = \"spinner\", size = \"md\", text, className }: LoadingProps) {\n  const renderLoading = () => {\n    switch (variant) {\n      case \"skeleton\":\n        return <SkeletonLoading className={cn(loadingVariants({ size }), className)} />;\n      case \"dots\":\n        return <DotsLoading size={size} className={className} />;\n      case \"spinner\":\n      default:\n        return <SpinnerLoading size={size} className={className} />;\n    }\n  };\n\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center gap-2\", className)}>\n      {renderLoading()}\n      {text && (\n        <p className=\"text-sm text-muted-foreground animate-pulse\" aria-live=\"polite\">\n          {text}\n        </p>\n      )}\n    </div>\n  );\n}\n\n// Individual loading components for specific use cases\nexport function LoadingSpinner({ size = \"md\", className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  return <SpinnerLoading size={size} className={className} />;\n}\n\nexport function LoadingSkeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {\n  return <SkeletonLoading className={className} {...props} />;\n}\n\nexport function LoadingDots({ size = \"md\", className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  return <DotsLoading size={size} className={className} />;\n}\n\n// Skeleton variants for different content types\nexport function SkeletonText({ lines = 3, className }: { lines?: number; className?: string }) {\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      {Array.from({ length: lines }).map((_, index) => (\n        <SkeletonLoading\n          key={index}\n          className={cn(\n            \"h-4\",\n            index === lines - 1 ? \"w-3/4\" : \"w-full\" // Last line is shorter\n          )}\n        />\n      ))}\n    </div>\n  );\n}\n\nexport function SkeletonCard({ className }: { className?: string }) {\n  return (\n    <div className={cn(\"space-y-4 p-4\", className)}>\n      <SkeletonLoading className=\"h-48 w-full\" />\n      <div className=\"space-y-2\">\n        <SkeletonLoading className=\"h-4 w-3/4\" />\n        <SkeletonLoading className=\"h-4 w-1/2\" />\n      </div>\n    </div>\n  );\n}\n\nexport function SkeletonAvatar({ size = \"md\", className }: { size?: \"sm\" | \"md\" | \"lg\"; className?: string }) {\n  const sizeClasses = {\n    sm: \"h-8 w-8\",\n    md: \"h-10 w-10\",\n    lg: \"h-12 w-12\",\n  };\n\n  return (\n    <SkeletonLoading\n      className={cn(\"rounded-full\", sizeClasses[size], className)}\n    />\n  );\n}\n\n// Loading overlay for full-screen loading states\nexport function LoadingOverlay({ \n  isVisible, \n  text = \"Loading...\", \n  className \n}: { \n  isVisible: boolean; \n  text?: string; \n  className?: string; \n}) {\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      className={cn(\n        \"fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm\",\n        className\n      )}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.2 }}\n    >\n      <div className=\"flex flex-col items-center space-y-4\">\n        <LoadingSpinner size=\"lg\" />\n        <p className=\"text-lg font-medium\" aria-live=\"polite\">\n          {text}\n        </p>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AASA,MAAM,kBAAkB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACxB,oCACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAQF,4BAA4B;AAC5B,SAAS,eAAe,KAAsE;QAAtE,EAAE,IAAI,EAAE,SAAS,EAAqD,GAAtE;IACtB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oDACA,WAAW,CAAC,QAAQ,KAAK,EACzB;QAEF,UAAU,sHAAA,CAAA,kBAAe;QACzB,SAAQ;QACR,eAAY;;;;;;AAGlB;KAnBS;AAqBT,6BAA6B;AAC7B,SAAS,gBAAgB,KAA6D;QAA7D,EAAE,SAAS,EAAE,GAAG,OAA6C,GAA7D;IACvB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;MAPS;AAST,yBAAyB;AACzB,SAAS,YAAY,KAAsE;QAAtE,EAAE,IAAI,EAAE,SAAS,EAAqD,GAAtE;IACnB,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,QAAQ,CAAC,QAAQ,KAAK;IAEtC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;gBACzC,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;gBACA,eAAY;eAXP;;;;;;;;;;AAgBf;MA7BS;AAgCF,SAAS,QAAQ,KAAmE;QAAnE,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,IAAI,EAAE,SAAS,EAAgB,GAAnE;IACtB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAgB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;wBAAE;oBAAK,IAAI;;;;;;YACnE,KAAK;gBACH,qBAAO,6LAAC;oBAAY,MAAM;oBAAM,WAAW;;;;;;YAC7C,KAAK;YACL;gBACE,qBAAO,6LAAC;oBAAe,MAAM;oBAAM,WAAW;;;;;;QAClD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;;YACnE;YACA,sBACC,6LAAC;gBAAE,WAAU;gBAA8C,aAAU;0BAClE;;;;;;;;;;;;AAKX;MAvBgB;AA0BT,SAAS,eAAe,KAA6E;QAA7E,EAAE,OAAO,IAAI,EAAE,SAAS,EAAqD,GAA7E;IAC7B,qBAAO,6LAAC;QAAe,MAAM;QAAM,WAAW;;;;;;AAChD;MAFgB;AAIT,SAAS,gBAAgB,KAA6D;QAA7D,EAAE,SAAS,EAAE,GAAG,OAA6C,GAA7D;IAC9B,qBAAO,6LAAC;QAAgB,WAAW;QAAY,GAAG,KAAK;;;;;;AACzD;MAFgB;AAIT,SAAS,YAAY,KAA6E;QAA7E,EAAE,OAAO,IAAI,EAAE,SAAS,EAAqD,GAA7E;IAC1B,qBAAO,6LAAC;QAAY,MAAM;QAAM,WAAW;;;;;;AAC7C;MAFgB;AAKT,SAAS,aAAa,KAAgE;QAAhE,EAAE,QAAQ,CAAC,EAAE,SAAS,EAA0C,GAAhE;IAC3B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,UAAU,QAAQ,IAAI,UAAU,SAAS,uBAAuB;;eAH7D;;;;;;;;;;AASf;MAdgB;AAgBT,SAAS,aAAa,KAAqC;QAArC,EAAE,SAAS,EAA0B,GAArC;IAC3B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;0BAClC,6LAAC;gBAAgB,WAAU;;;;;;0BAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAgB,WAAU;;;;;;kCAC3B,6LAAC;wBAAgB,WAAU;;;;;;;;;;;;;;;;;;AAInC;MAVgB;AAYT,SAAS,eAAe,KAA6E;QAA7E,EAAE,OAAO,IAAI,EAAE,SAAS,EAAqD,GAA7E;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,WAAW,CAAC,KAAK,EAAE;;;;;;AAGvD;MAZgB;AAeT,SAAS,eAAe,KAQ9B;QAR8B,EAC7B,SAAS,EACT,OAAO,YAAY,EACnB,SAAS,EAKV,GAR8B;IAS7B,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yFACA;QAEF,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;;;;;;8BACrB,6LAAC;oBAAE,WAAU;oBAAsB,aAAU;8BAC1C;;;;;;;;;;;;;;;;;AAKX;OA9BgB", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/utils/accessibility.ts"], "sourcesContent": ["// Accessibility utility functions for WCAG 2.1 AA compliance\n\n// Focus management utilities\nexport const trapFocus = (element: HTMLElement): (() => void) => {\n  const focusableElements = element.querySelectorAll(\n    'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n  );\n  \n  const firstFocusableElement = focusableElements[0] as HTMLElement;\n  const lastFocusableElement = focusableElements[focusableElements.length - 1] as HTMLElement;\n\n  const handleTabKey = (e: KeyboardEvent) => {\n    if (e.key !== 'Tab') return;\n\n    if (e.shiftKey) {\n      if (document.activeElement === firstFocusableElement) {\n        lastFocusableElement.focus();\n        e.preventDefault();\n      }\n    } else {\n      if (document.activeElement === lastFocusableElement) {\n        firstFocusableElement.focus();\n        e.preventDefault();\n      }\n    }\n  };\n\n  element.addEventListener('keydown', handleTabKey);\n  firstFocusableElement?.focus();\n\n  return () => {\n    element.removeEventListener('keydown', handleTabKey);\n  };\n};\n\n// Escape key handler for modals and dropdowns\nexport const handleEscapeKey = (callback: () => void) => {\n  const handleKeyDown = (e: KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      callback();\n    }\n  };\n\n  document.addEventListener('keydown', handleKeyDown);\n  \n  return () => {\n    document.removeEventListener('keydown', handleKeyDown);\n  };\n};\n\n// Generate unique IDs for form elements\nexport const generateId = (prefix: string = 'id'): string => {\n  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;\n};\n\n// Screen reader announcements\nexport const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {\n  const announcement = document.createElement('div');\n  announcement.setAttribute('aria-live', priority);\n  announcement.setAttribute('aria-atomic', 'true');\n  announcement.setAttribute('class', 'sr-only');\n  announcement.textContent = message;\n  \n  document.body.appendChild(announcement);\n  \n  setTimeout(() => {\n    document.body.removeChild(announcement);\n  }, 1000);\n};\n\n// Color contrast validation (simplified)\nexport const hasGoodContrast = (foreground: string, background: string): boolean => {\n  // This is a simplified version - in production, use a proper color contrast library\n  // For now, we'll assume our design system colors meet WCAG AA standards\n  return true;\n};\n\n// Skip link functionality\nexport const createSkipLink = (targetId: string, text: string = 'Skip to main content'): HTMLElement => {\n  const skipLink = document.createElement('a');\n  skipLink.href = `#${targetId}`;\n  skipLink.textContent = text;\n  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded';\n  \n  return skipLink;\n};\n\n// Keyboard navigation helpers\nexport const isNavigationKey = (key: string): boolean => {\n  return ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(key);\n};\n\nexport const isActionKey = (key: string): boolean => {\n  return ['Enter', ' '].includes(key);\n};\n\n// ARIA attributes helpers\nexport const getAriaAttributes = (props: {\n  expanded?: boolean;\n  selected?: boolean;\n  disabled?: boolean;\n  required?: boolean;\n  invalid?: boolean;\n  describedBy?: string;\n  labelledBy?: string;\n  controls?: string;\n  owns?: string;\n}) => {\n  const attributes: Record<string, string | boolean> = {};\n  \n  if (props.expanded !== undefined) attributes['aria-expanded'] = props.expanded;\n  if (props.selected !== undefined) attributes['aria-selected'] = props.selected;\n  if (props.disabled !== undefined) attributes['aria-disabled'] = props.disabled;\n  if (props.required !== undefined) attributes['aria-required'] = props.required;\n  if (props.invalid !== undefined) attributes['aria-invalid'] = props.invalid;\n  if (props.describedBy) attributes['aria-describedby'] = props.describedBy;\n  if (props.labelledBy) attributes['aria-labelledby'] = props.labelledBy;\n  if (props.controls) attributes['aria-controls'] = props.controls;\n  if (props.owns) attributes['aria-owns'] = props.owns;\n  \n  return attributes;\n};\n\n// Focus visible utilities\nexport const addFocusVisiblePolyfill = () => {\n  // Simple focus-visible polyfill for older browsers\n  let hadKeyboardEvent = true;\n  \n  const keyboardThrottleTimeout = 100;\n  let keyboardThrottleTimeoutID = 0;\n  \n  const pointerInitialPress = (e: PointerEvent) => {\n    if (e.pointerType === 'mouse' || e.pointerType === 'touch') {\n      hadKeyboardEvent = false;\n    }\n  };\n  \n  const onKeyDown = (e: KeyboardEvent) => {\n    if (e.metaKey || e.altKey || e.ctrlKey) {\n      return;\n    }\n    \n    hadKeyboardEvent = true;\n    clearTimeout(keyboardThrottleTimeoutID);\n    keyboardThrottleTimeoutID = window.setTimeout(() => {\n      hadKeyboardEvent = false;\n    }, keyboardThrottleTimeout);\n  };\n  \n  const onFocus = (e: FocusEvent) => {\n    const target = e.target as HTMLElement;\n    if (hadKeyboardEvent || target.matches(':focus-visible')) {\n      target.classList.add('focus-visible');\n    }\n  };\n  \n  const onBlur = (e: FocusEvent) => {\n    const target = e.target as HTMLElement;\n    target.classList.remove('focus-visible');\n  };\n  \n  document.addEventListener('keydown', onKeyDown, true);\n  document.addEventListener('pointerdown', pointerInitialPress, true);\n  document.addEventListener('focus', onFocus, true);\n  document.addEventListener('blur', onBlur, true);\n};\n\n// Reduced motion detection\nexport const prefersReducedMotion = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n};\n\n// High contrast detection\nexport const prefersHighContrast = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.matchMedia('(prefers-contrast: high)').matches;\n};\n\n// Text size utilities\nexport const getTextSizeClass = (size: 'sm' | 'base' | 'lg' | 'xl'): string => {\n  const sizes = {\n    sm: 'text-sm',\n    base: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n  return sizes[size];\n};\n\n// Semantic HTML helpers\nexport const getSemanticRole = (element: string): string => {\n  const roles: Record<string, string> = {\n    'nav': 'navigation',\n    'main': 'main',\n    'aside': 'complementary',\n    'section': 'region',\n    'article': 'article',\n    'header': 'banner',\n    'footer': 'contentinfo'\n  };\n  \n  return roles[element] || '';\n};\n\n// Form validation messages\nexport const getValidationMessage = (field: string, error: string): string => {\n  const messages: Record<string, Record<string, string>> = {\n    email: {\n      required: 'Email address is required',\n      invalid: 'Please enter a valid email address'\n    },\n    phone: {\n      required: 'Phone number is required',\n      invalid: 'Please enter a valid phone number'\n    },\n    name: {\n      required: 'Name is required',\n      minLength: 'Name must be at least 2 characters long'\n    }\n  };\n  \n  return messages[field]?.[error] || 'This field is invalid';\n};\n"], "names": [], "mappings": "AAAA,6DAA6D;AAE7D,6BAA6B;;;;;;;;;;;;;;;;;;AACtB,MAAM,YAAY,CAAC;IACxB,MAAM,oBAAoB,QAAQ,gBAAgB,CAChD;IAGF,MAAM,wBAAwB,iBAAiB,CAAC,EAAE;IAClD,MAAM,uBAAuB,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;IAE5E,MAAM,eAAe,CAAC;QACpB,IAAI,EAAE,GAAG,KAAK,OAAO;QAErB,IAAI,EAAE,QAAQ,EAAE;YACd,IAAI,SAAS,aAAa,KAAK,uBAAuB;gBACpD,qBAAqB,KAAK;gBAC1B,EAAE,cAAc;YAClB;QACF,OAAO;YACL,IAAI,SAAS,aAAa,KAAK,sBAAsB;gBACnD,sBAAsB,KAAK;gBAC3B,EAAE,cAAc;YAClB;QACF;IACF;IAEA,QAAQ,gBAAgB,CAAC,WAAW;IACpC,kCAAA,4CAAA,sBAAuB,KAAK;IAE5B,OAAO;QACL,QAAQ,mBAAmB,CAAC,WAAW;IACzC;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,SAAS,gBAAgB,CAAC,WAAW;IAErC,OAAO;QACL,SAAS,mBAAmB,CAAC,WAAW;IAC1C;AACF;AAGO,MAAM,aAAa;QAAC,0EAAiB;IAC1C,OAAO,AAAC,GAAY,OAAV,QAAO,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC3D;AAGO,MAAM,yBAAyB,SAAC;QAAiB,4EAAmC;IACzF,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,YAAY,CAAC,aAAa;IACvC,aAAa,YAAY,CAAC,eAAe;IACzC,aAAa,YAAY,CAAC,SAAS;IACnC,aAAa,WAAW,GAAG;IAE3B,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,WAAW;QACT,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG;AACL;AAGO,MAAM,kBAAkB,CAAC,YAAoB;IAClD,oFAAoF;IACpF,wEAAwE;IACxE,OAAO;AACT;AAGO,MAAM,iBAAiB,SAAC;QAAkB,wEAAe;IAC9D,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,IAAI,GAAG,AAAC,IAAY,OAAT;IACpB,SAAS,WAAW,GAAG;IACvB,SAAS,SAAS,GAAG;IAErB,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO;QAAC;QAAW;QAAa;QAAa;QAAc;QAAQ;KAAM,CAAC,QAAQ,CAAC;AACrF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO;QAAC;QAAS;KAAI,CAAC,QAAQ,CAAC;AACjC;AAGO,MAAM,oBAAoB,CAAC;IAWhC,MAAM,aAA+C,CAAC;IAEtD,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,QAAQ,KAAK,WAAW,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAC9E,IAAI,MAAM,OAAO,KAAK,WAAW,UAAU,CAAC,eAAe,GAAG,MAAM,OAAO;IAC3E,IAAI,MAAM,WAAW,EAAE,UAAU,CAAC,mBAAmB,GAAG,MAAM,WAAW;IACzE,IAAI,MAAM,UAAU,EAAE,UAAU,CAAC,kBAAkB,GAAG,MAAM,UAAU;IACtE,IAAI,MAAM,QAAQ,EAAE,UAAU,CAAC,gBAAgB,GAAG,MAAM,QAAQ;IAChE,IAAI,MAAM,IAAI,EAAE,UAAU,CAAC,YAAY,GAAG,MAAM,IAAI;IAEpD,OAAO;AACT;AAGO,MAAM,0BAA0B;IACrC,mDAAmD;IACnD,IAAI,mBAAmB;IAEvB,MAAM,0BAA0B;IAChC,IAAI,4BAA4B;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,SAAS;YAC1D,mBAAmB;QACrB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,EAAE,OAAO,IAAI,EAAE,MAAM,IAAI,EAAE,OAAO,EAAE;YACtC;QACF;QAEA,mBAAmB;QACnB,aAAa;QACb,4BAA4B,OAAO,UAAU,CAAC;YAC5C,mBAAmB;QACrB,GAAG;IACL;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,SAAS,EAAE,MAAM;QACvB,IAAI,oBAAoB,OAAO,OAAO,CAAC,mBAAmB;YACxD,OAAO,SAAS,CAAC,GAAG,CAAC;QACvB;IACF;IAEA,MAAM,SAAS,CAAC;QACd,MAAM,SAAS,EAAE,MAAM;QACvB,OAAO,SAAS,CAAC,MAAM,CAAC;IAC1B;IAEA,SAAS,gBAAgB,CAAC,WAAW,WAAW;IAChD,SAAS,gBAAgB,CAAC,eAAe,qBAAqB;IAC9D,SAAS,gBAAgB,CAAC,SAAS,SAAS;IAC5C,SAAS,gBAAgB,CAAC,QAAQ,QAAQ;AAC5C;AAGO,MAAM,uBAAuB;IAClC;;IACA,OAAO,OAAO,UAAU,CAAC,oCAAoC,OAAO;AACtE;AAGO,MAAM,sBAAsB;IACjC;;IACA,OAAO,OAAO,UAAU,CAAC,4BAA4B,OAAO;AAC9D;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,QAAQ;QACZ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;IACN;IACA,OAAO,KAAK,CAAC,KAAK;AACpB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,QAAgC;QACpC,OAAO;QACP,QAAQ;QACR,SAAS;QACT,WAAW;QACX,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IAEA,OAAO,KAAK,CAAC,QAAQ,IAAI;AAC3B;AAGO,MAAM,uBAAuB,CAAC,OAAe;QAgB3C;IAfP,MAAM,WAAmD;QACvD,OAAO;YACL,UAAU;YACV,SAAS;QACX;QACA,OAAO;YACL,UAAU;YACV,SAAS;QACX;QACA,MAAM;YACJ,UAAU;YACV,WAAW;QACb;IACF;IAEA,OAAO,EAAA,kBAAA,QAAQ,CAAC,MAAM,cAAf,sCAAA,eAAiB,CAAC,MAAM,KAAI;AACrC", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/boarding-house-landing.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport dynamic from \"next/dynamic\"\nimport { motion } from \"framer-motion\"\nimport { Navigation } from \"./navigation\"\nimport { Footer } from \"./footer\"\nimport { Loading, LoadingOverlay } from \"./ui/loading\"\nimport { prefersReducedMotion } from \"@/utils/accessibility\"\n\n// Lazy load sections for better performance\nconst HeroSection = dynamic(() => import(\"./sections/hero\").then(mod => ({ default: mod.HeroSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"min-h-screen\" />,\n  ssr: true\n})\n\nconst RoomsSection = dynamic(() => import(\"./sections/rooms\").then(mod => ({ default: mod.RoomsSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"h-96\" />,\n  ssr: false\n})\n\nconst FacilitiesSection = dynamic(() => import(\"./sections/facilities\").then(mod => ({ default: mod.FacilitiesSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"h-96\" />,\n  ssr: false\n})\n\nconst TestimonialsSection = dynamic(() => import(\"./sections/testimonials\").then(mod => ({ default: mod.TestimonialsSection })), {\n  loading: () => <Loading variant=\"skeleton\" className=\"h-96\" />,\n  ssr: false\n})\n\ninterface BoardingHouseLandingProps {\n  className?: string;\n}\n\nexport function BoardingHouseLanding({ className }: BoardingHouseLandingProps) {\n  const [isLoading, setIsLoading] = React.useState(true)\n  const [loadedSections, setLoadedSections] = React.useState<Set<string>>(new Set())\n  const [reducedMotion, setReducedMotion] = React.useState(false)\n\n  // Check for reduced motion preference\n  React.useEffect(() => {\n    setReducedMotion(prefersReducedMotion())\n    \n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')\n    const handleChange = () => setReducedMotion(mediaQuery.matches)\n    \n    mediaQuery.addEventListener('change', handleChange)\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [])\n\n  // Handle initial loading\n  React.useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 1000)\n\n    return () => clearTimeout(timer)\n  }, [])\n\n  // Preload critical images\n  React.useEffect(() => {\n    const criticalImages = [\n      '/images/hero-1.jpg',\n      '/images/hero-2.jpg',\n      '/images/hero-3.jpg'\n    ]\n\n    criticalImages.forEach(src => {\n      const img = new Image()\n      img.src = src\n    })\n  }, [])\n\n  // Intersection Observer for lazy loading sections\n  React.useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            const sectionId = entry.target.id\n            setLoadedSections(prev => new Set([...prev, sectionId]))\n          }\n        })\n      },\n      {\n        rootMargin: '100px 0px',\n        threshold: 0.1\n      }\n    )\n\n    // Observe all sections\n    const sections = document.querySelectorAll('section[id]')\n    sections.forEach(section => observer.observe(section))\n\n    return () => observer.disconnect()\n  }, [])\n\n  // Skip link for accessibility\n  React.useEffect(() => {\n    const skipLink = document.createElement('a')\n    skipLink.href = '#main-content'\n    skipLink.textContent = 'Skip to main content'\n    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded'\n    \n    document.body.insertBefore(skipLink, document.body.firstChild)\n    \n    return () => {\n      if (document.body.contains(skipLink)) {\n        document.body.removeChild(skipLink)\n      }\n    }\n  }, [])\n\n  return (\n    <div className={`min-h-screen bg-white ${className}`}>\n      {/* Loading Overlay */}\n      <LoadingOverlay \n        isVisible={isLoading} \n        text=\"Welcome to BoardingHouse\"\n      />\n\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Main Content */}\n      <main id=\"main-content\" role=\"main\">\n        {/* Hero Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"min-h-screen\" />}>\n          <HeroSection />\n        </React.Suspense>\n\n        {/* Rooms Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"h-96\" />}>\n          <RoomsSection />\n        </React.Suspense>\n\n        {/* Facilities Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"h-96\" />}>\n          <FacilitiesSection />\n        </React.Suspense>\n\n        {/* Testimonials Section */}\n        <React.Suspense fallback={<Loading variant=\"skeleton\" className=\"h-96\" />}>\n          <TestimonialsSection />\n        </React.Suspense>\n      </main>\n\n      {/* Footer */}\n      <Footer />\n\n      {/* Performance Monitoring (Development Only) */}\n      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}\n    </div>\n  )\n}\n\n// Performance monitoring component for development\nfunction PerformanceMonitor() {\n  const [metrics, setMetrics] = React.useState<any>(null)\n\n  React.useEffect(() => {\n    if (typeof window === 'undefined' || !('performance' in window)) return\n\n    const observer = new PerformanceObserver((list) => {\n      const entries = list.getEntries()\n      entries.forEach(entry => {\n        console.log(`Performance: ${entry.name} - ${entry.duration}ms`)\n      })\n    })\n\n    observer.observe({ entryTypes: ['measure', 'navigation'] })\n\n    // Monitor memory usage if available\n    if ('memory' in performance) {\n      const updateMemory = () => {\n        const memory = (performance as any).memory\n        setMetrics({\n          usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1048576),\n          totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1048576),\n          jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1048576)\n        })\n      }\n\n      updateMemory()\n      const interval = setInterval(updateMemory, 5000)\n\n      return () => {\n        clearInterval(interval)\n        observer.disconnect()\n      }\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  if (!metrics) return null\n\n  return (\n    <div className=\"fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs z-50\">\n      <div>Memory: {metrics.usedJSHeapSize}MB / {metrics.totalJSHeapSize}MB</div>\n      <div>Limit: {metrics.jsHeapSizeLimit}MB</div>\n    </div>\n  )\n}\n\n// Error Boundary for better error handling\nexport class BoardingHouseErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('BoardingHouse Error:', error, errorInfo)\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"text-center space-y-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Something went wrong</h1>\n            <p className=\"text-gray-600\">We're sorry, but something unexpected happened.</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n            >\n              Reload Page\n            </button>\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// HOC for performance optimization\nexport function withPerformanceOptimization<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const OptimizedComponent = React.memo((props: P) => {\n    return <Component {...props} />\n  })\n\n  OptimizedComponent.displayName = `withPerformanceOptimization(${Component.displayName || Component.name})`\n  \n  return OptimizedComponent\n}\n\n// Custom hook for intersection observer\nexport function useIntersectionObserver(\n  elementRef: React.RefObject<Element>,\n  options: IntersectionObserverInit = {}\n) {\n  const [isIntersecting, setIsIntersecting] = React.useState(false)\n\n  React.useEffect(() => {\n    const element = elementRef.current\n    if (!element) return\n\n    const observer = new IntersectionObserver(\n      ([entry]) => setIsIntersecting(entry.isIntersecting),\n      {\n        threshold: 0.1,\n        rootMargin: '50px',\n        ...options\n      }\n    )\n\n    observer.observe(element)\n    return () => observer.unobserve(element)\n  }, [elementRef, options])\n\n  return isIntersecting\n}\n\n// Service Worker registration for PWA capabilities\nexport function registerServiceWorker() {\n  if (\n    typeof window !== 'undefined' &&\n    'serviceWorker' in navigator &&\n    process.env.NODE_ENV === 'production'\n  ) {\n    window.addEventListener('load', async () => {\n      try {\n        const registration = await navigator.serviceWorker.register('/sw.js')\n        console.log('SW registered: ', registration)\n      } catch (registrationError) {\n        console.log('SW registration failed: ', registrationError)\n      }\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAwJO;;AAtJP;AACA;AAEA;AACA;AACA;AACA;;;;;;;AARA;;;;;;;AAUA,4CAA4C;AAC5C,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,IAAM,uJAA0B,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,WAAW;QAAC,CAAC;;;;;;IACnG,SAAS,kBAAM,6LAAC,+HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;KAFD;AAKN,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,IAAM,wJAA2B,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,YAAY;QAAC,CAAC;;;;;;IACtG,SAAS,kBAAM,6LAAC,+HAA<PERSON>,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;MAFD;AAKN,MAAM,oBAAoB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,IAAM,6JAAgC,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,iBAAiB;QAAC,CAAC;;;;;;IACrH,SAAS,kBAAM,6LAAC,+HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;MAFD;AAKN,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,IAAM,+JAAkC,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,mBAAmB;QAAC,CAAC;;;;;;IAC3H,SAAS,kBAAM,6LAAC,+HAAA,CAAA,UAAO;YAAC,SAAQ;YAAW,WAAU;;;;;;IACrD,KAAK;;MAFD;AASC,SAAS,qBAAqB,KAAwC;QAAxC,EAAE,SAAS,EAA6B,GAAxC;;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,WAAc,CAAC;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,WAAc,CAAc,IAAI;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEzD,sCAAsC;IACtC,6JAAA,CAAA,YAAe;0CAAC;YACd,iBAAiB,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;YAEpC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;+DAAe,IAAM,iBAAiB,WAAW,OAAO;;YAE9D,WAAW,gBAAgB,CAAC,UAAU;YACtC;kDAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;yCAAG,EAAE;IAEL,yBAAyB;IACzB,6JAAA,CAAA,YAAe;0CAAC;YACd,MAAM,QAAQ;wDAAW;oBACvB,aAAa;gBACf;uDAAG;YAEH;kDAAO,IAAM,aAAa;;QAC5B;yCAAG,EAAE;IAEL,0BAA0B;IAC1B,6JAAA,CAAA,YAAe;0CAAC;YACd,MAAM,iBAAiB;gBACrB;gBACA;gBACA;aACD;YAED,eAAe,OAAO;kDAAC,CAAA;oBACrB,MAAM,MAAM,IAAI;oBAChB,IAAI,GAAG,GAAG;gBACZ;;QACF;yCAAG,EAAE;IAEL,kDAAkD;IAClD,6JAAA,CAAA,YAAe;0CAAC;YACd,MAAM,WAAW,IAAI;kDACnB,CAAC;oBACC,QAAQ,OAAO;0DAAC,CAAA;4BACd,IAAI,MAAM,cAAc,EAAE;gCACxB,MAAM,YAAY,MAAM,MAAM,CAAC,EAAE;gCACjC;sEAAkB,CAAA,OAAQ,IAAI,IAAI;+CAAI;4CAAM;yCAAU;;4BACxD;wBACF;;gBACF;iDACA;gBACE,YAAY;gBACZ,WAAW;YACb;YAGF,uBAAuB;YACvB,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,SAAS,OAAO;kDAAC,CAAA,UAAW,SAAS,OAAO,CAAC;;YAE7C;kDAAO,IAAM,SAAS,UAAU;;QAClC;yCAAG,EAAE;IAEL,8BAA8B;IAC9B,6JAAA,CAAA,YAAe;0CAAC;YACd,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,IAAI,GAAG;YAChB,SAAS,WAAW,GAAG;YACvB,SAAS,SAAS,GAAG;YAErB,SAAS,IAAI,CAAC,YAAY,CAAC,UAAU,SAAS,IAAI,CAAC,UAAU;YAE7D;kDAAO;oBACL,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW;wBACpC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;gBACF;;QACF;yCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,AAAC,yBAAkC,OAAV;;0BAEvC,6LAAC,+HAAA,CAAA,iBAAc;gBACb,WAAW;gBACX,MAAK;;;;;;0BAIP,6LAAC,4HAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAK,IAAG;gBAAe,MAAK;;kCAE3B,6LAAC,6JAAA,CAAA,WAAc;wBAAC,wBAAU,6LAAC,+HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,6LAAC;;;;;;;;;;kCAIH,6LAAC,6JAAA,CAAA,WAAc;wBAAC,wBAAU,6LAAC,+HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,6LAAC;;;;;;;;;;kCAIH,6LAAC,6JAAA,CAAA,WAAc;wBAAC,wBAAU,6LAAC,+HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,6LAAC;;;;;;;;;;kCAIH,6LAAC,6JAAA,CAAA,WAAc;wBAAC,wBAAU,6LAAC,+HAAA,CAAA,UAAO;4BAAC,SAAQ;4BAAW,WAAU;;;;;;kCAC9D,cAAA,6LAAC;;;;;;;;;;;;;;;;0BAKL,6LAAC,wHAAA,CAAA,SAAM;;;;;YAGN,oDAAyB,+BAAiB,6LAAC;;;;;;;;;;;AAGlD;GAxHgB;MAAA;AA0HhB,mDAAmD;AACnD,SAAS;;IACP,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,WAAc,CAAM;IAElD,6JAAA,CAAA,YAAe;wCAAC;YACd,IAAI,aAAkB,eAAe,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAEjE,MAAM,WAAW,IAAI;gDAAoB,CAAC;oBACxC,MAAM,UAAU,KAAK,UAAU;oBAC/B,QAAQ,OAAO;wDAAC,CAAA;4BACd,QAAQ,GAAG,CAAC,AAAC,gBAA+B,OAAhB,MAAM,IAAI,EAAC,OAAoB,OAAf,MAAM,QAAQ,EAAC;wBAC7D;;gBACF;;YAEA,SAAS,OAAO,CAAC;gBAAE,YAAY;oBAAC;oBAAW;iBAAa;YAAC;YAEzD,oCAAoC;YACpC,IAAI,YAAY,aAAa;gBAC3B,MAAM;iEAAe;wBACnB,MAAM,SAAS,AAAC,YAAoB,MAAM;wBAC1C,WAAW;4BACT,gBAAgB,KAAK,KAAK,CAAC,OAAO,cAAc,GAAG;4BACnD,iBAAiB,KAAK,KAAK,CAAC,OAAO,eAAe,GAAG;4BACrD,iBAAiB,KAAK,KAAK,CAAC,OAAO,eAAe,GAAG;wBACvD;oBACF;;gBAEA;gBACA,MAAM,WAAW,YAAY,cAAc;gBAE3C;oDAAO;wBACL,cAAc;wBACd,SAAS,UAAU;oBACrB;;YACF;YAEA;gDAAO,IAAM,SAAS,UAAU;;QAClC;uCAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;oBAAI;oBAAS,QAAQ,cAAc;oBAAC;oBAAM,QAAQ,eAAe;oBAAC;;;;;;;0BACnE,6LAAC;;oBAAI;oBAAQ,QAAQ,eAAe;oBAAC;;;;;;;;;;;;;AAG3C;IA9CS;MAAA;AAiDF,MAAM,mCAAmC,6JAAA,CAAA,YAAe;IAS7D,OAAO,yBAAyB,KAAY,EAAE;QAC5C,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,wBAAwB,OAAO;IAC/C;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAhCA,YAAY,KAAoC,CAAE;QAChD,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AA8BF;AAGO,SAAS,4BACd,SAAiC;IAEjC,MAAM,mCAAqB,6JAAA,CAAA,OAAU,CAAC,CAAC;QACrC,qBAAO,6LAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;IAEA,mBAAmB,WAAW,GAAG,AAAC,+BAAsE,OAAxC,UAAU,WAAW,IAAI,UAAU,IAAI,EAAC;IAExG,OAAO;AACT;AAGO,SAAS,wBACd,UAAoC;QACpC,UAAA,iEAAoC,CAAC;;IAErC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,WAAc,CAAC;IAE3D,6JAAA,CAAA,YAAe;6CAAC;YACd,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,IAAI;qDACnB;wBAAC,CAAC,MAAM;2BAAK,kBAAkB,MAAM,cAAc;;oDACnD;gBACE,WAAW;gBACX,YAAY;gBACZ,GAAG,OAAO;YACZ;YAGF,SAAS,OAAO,CAAC;YACjB;qDAAO,IAAM,SAAS,SAAS,CAAC;;QAClC;4CAAG;QAAC;QAAY;KAAQ;IAExB,OAAO;AACT;IAxBgB;AA2BT,SAAS;IACd,IACE,aAAkB,eAClB,mBAAmB,aACnB,oDAAyB;;AAW7B", "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/next/src/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactElement } from 'react'\nimport { BailoutToCSRError } from './bailout-to-csr'\n\ninterface BailoutToCSRProps {\n  reason: string\n  children: ReactElement\n}\n\n/**\n * If rendered on the server, this component throws an error\n * to signal Next.js that it should bail out to client-side rendering instead.\n */\nexport function BailoutToCSR({ reason, children }: BailoutToCSRProps) {\n  if (typeof window === 'undefined') {\n    throw new BailoutToCSRError(reason)\n  }\n\n  return children\n}\n"], "names": ["BailoutToCSR", "reason", "children", "window", "BailoutToCSRError"], "mappings": ";;;+BAcgBA,gBAAAA;;;eAAAA;;;8BAXkB;AAW3B,SAASA,aAAa,KAAuC;IAAvC,IAAA,EAAEC,MAAM,EAAEC,QAAQ,EAAqB,GAAvC;IAC3B,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,OAAA,cAA6B,CAA7B,IAAIC,cAAAA,iBAAiB,CAACH,SAAtB,qBAAA;mBAAA;wBAAA;0BAAA;QAA4B;IACpC;IAEA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/next/src/shared/lib/encode-uri-path.ts"], "sourcesContent": ["export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n"], "names": ["encodeURIPath", "file", "split", "map", "p", "encodeURIComponent", "join"], "mappings": ";;;+BAAgBA,iBAAAA;;;eAAAA;;;AAAT,SAASA,cAAcC,IAAY;IACxC,OAAOA,KACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,IAC9BE,IAAI,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/next/src/shared/lib/lazy-dynamic/preload-chunks.tsx"], "sourcesContent": ["'use client'\n\nimport { preload } from 'react-dom'\n\nimport { workAsyncStorage } from '../../../server/app-render/work-async-storage.external'\nimport { encodeURIPath } from '../encode-uri-path'\n\nexport function PreloadChunks({\n  moduleIds,\n}: {\n  moduleIds: string[] | undefined\n}) {\n  // Early return in client compilation and only load requestStore on server side\n  if (typeof window !== 'undefined') {\n    return null\n  }\n\n  const workStore = workAsyncStorage.getStore()\n  if (workStore === undefined) {\n    return null\n  }\n\n  const allFiles = []\n\n  // Search the current dynamic call unique key id in react loadable manifest,\n  // and find the corresponding CSS files to preload\n  if (workStore.reactLoadableManifest && moduleIds) {\n    const manifest = workStore.reactLoadableManifest\n    for (const key of moduleIds) {\n      if (!manifest[key]) continue\n      const chunks = manifest[key].files\n      allFiles.push(...chunks)\n    }\n  }\n\n  if (allFiles.length === 0) {\n    return null\n  }\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n\n  return (\n    <>\n      {allFiles.map((chunk) => {\n        const href = `${workStore.assetPrefix}/_next/${encodeURIPath(chunk)}${dplId}`\n        const isCss = chunk.endsWith('.css')\n        // If it's stylesheet we use `precedence` o help hoist with React Float.\n        // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n        // The `preload` for stylesheet is not optional.\n        if (isCss) {\n          return (\n            <link\n              key={chunk}\n              // @ts-ignore\n              precedence=\"dynamic\"\n              href={href}\n              rel=\"stylesheet\"\n              as=\"style\"\n            />\n          )\n        } else {\n          // If it's script we use ReactDOM.preload to preload the resources\n          preload(href, {\n            as: 'script',\n            fetchPriority: 'low',\n          })\n          return null\n        }\n      })}\n    </>\n  )\n}\n"], "names": ["PreloadChunks", "moduleIds", "window", "workStore", "workAsyncStorage", "getStore", "undefined", "allFiles", "reactLoadableManifest", "manifest", "key", "chunks", "files", "push", "length", "dplId", "process", "env", "NEXT_DEPLOYMENT_ID", "map", "chunk", "href", "assetPrefix", "encodeURIPath", "isCss", "endsWith", "link", "precedence", "rel", "as", "preload", "fetchPriority"], "mappings": "AAuCgBgB,QAAQC,GAAG,CAACC,kBAAkB;AAvC9C;;;;;+BAOgBlB,iBAAAA;;;eAAAA;;;;0BALQ;0CAES;+BACH;AAEvB,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,+EAA+E;IAC/E,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IAEA,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,IAAIF,cAAcG,WAAW;QAC3B,OAAO;IACT;IAEA,MAAMC,WAAW,EAAE;IAEnB,4EAA4E;IAC5E,kDAAkD;IAClD,IAAIJ,UAAUK,qBAAqB,IAAIP,WAAW;QAChD,MAAMQ,WAAWN,UAAUK,qBAAqB;QAChD,KAAK,MAAME,OAAOT,UAAW;YAC3B,IAAI,CAACQ,QAAQ,CAACC,IAAI,EAAE;YACpB,MAAMC,SAASF,QAAQ,CAACC,IAAI,CAACE,KAAK;YAClCL,SAASM,IAAI,IAAIF;QACnB;IACF;IAEA,IAAIJ,SAASO,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,MAAMC,8CACD,UAAOC,QAAQC,GAAG,CAACC,IACpB,cADsC;IAG1C,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBACGX,SAASY,GAAG,CAAC,CAACC;YACb,MAAMC,OAAUlB,UAAUmB,WAAW,GAAC,YAASC,CAAAA,GAAAA,eAAAA,aAAa,EAACH,SAASL;YACtE,MAAMS,QAAQJ,MAAMK,QAAQ,CAAC;YAC7B,wEAAwE;YACxE,0IAA0I;YAC1I,gDAAgD;YAChD,IAAID,OAAO;gBACT,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACE,QAAAA;oBAEC,aAAa;oBACbC,YAAW;oBACXN,MAAMA;oBACNO,KAAI;oBACJC,IAAG;mBALET;YAQX,OAAO;gBACL,kEAAkE;gBAClEU,CAAAA,GAAAA,UAAAA,OAAO,EAACT,MAAM;oBACZQ,IAAI;oBACJE,eAAe;gBACjB;gBACA,OAAO;YACT;QACF;;AAGN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/next/src/shared/lib/lazy-dynamic/loadable.tsx"], "sourcesContent": ["import { Suspense, Fragment, lazy } from 'react'\nimport { BailoutToCSR } from './dynamic-bailout-to-csr'\nimport type { ComponentModule } from './types'\nimport { PreloadChunks } from './preload-chunks'\n\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule<P>(\n  mod: React.ComponentType<P> | ComponentModule<P> | undefined\n): {\n  default: React.ComponentType<P>\n} {\n  // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n  // Cases:\n  // mod: { default: Component }\n  // mod: Component\n  // mod: { default: proxy(Component) }\n  // mod: proxy(Component)\n  const hasDefault = mod && 'default' in mod\n  return {\n    default: hasDefault\n      ? (mod as ComponentModule<P>).default\n      : (mod as React.ComponentType<P>),\n  }\n}\n\nconst defaultOptions = {\n  loader: () => Promise.resolve(convertModule(() => null)),\n  loading: null,\n  ssr: true,\n}\n\ninterface LoadableOptions {\n  loader?: () => Promise<React.ComponentType<any> | ComponentModule<any>>\n  loading?: React.ComponentType<any> | null\n  ssr?: boolean\n  modules?: string[]\n}\n\nfunction Loadable(options: LoadableOptions) {\n  const opts = { ...defaultOptions, ...options }\n  const Lazy = lazy(() => opts.loader().then(convertModule))\n  const Loading = opts.loading\n\n  function LoadableComponent(props: any) {\n    const fallbackElement = Loading ? (\n      <Loading isLoading={true} pastDelay={true} error={null} />\n    ) : null\n\n    // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n    const hasSuspenseBoundary = !opts.ssr || !!opts.loading\n    const Wrap = hasSuspenseBoundary ? Suspense : Fragment\n    const wrapProps = hasSuspenseBoundary ? { fallback: fallbackElement } : {}\n    const children = opts.ssr ? (\n      <>\n        {/* During SSR, we need to preload the CSS from the dynamic component to avoid flash of unstyled content */}\n        {typeof window === 'undefined' ? (\n          <PreloadChunks moduleIds={opts.modules} />\n        ) : null}\n        <Lazy {...props} />\n      </>\n    ) : (\n      <BailoutToCSR reason=\"next/dynamic\">\n        <Lazy {...props} />\n      </BailoutToCSR>\n    )\n\n    return <Wrap {...wrapProps}>{children}</Wrap>\n  }\n\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return LoadableComponent\n}\n\nexport default Loadable\n"], "names": ["convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "hasSuspenseBoundary", "Wrap", "Suspense", "Fragment", "wrapProps", "fallback", "children", "window", "PreloadChunks", "moduleIds", "modules", "BailoutToCSR", "reason", "displayName"], "mappings": ";;;+BA4EA,WAAA;;;eAAA;;;;uBA5EyC;qCACZ;+BAEC;AAE9B,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASA,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,qCAAqC;IACrC,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACJD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,OAAAA,WAAAA,GAAOC,CAAAA,GAAAA,OAAAA,IAAI,EAAC,IAAMF,KAAKP,MAAM,GAAGU,IAAI,CAACf;IAC3C,MAAMgB,UAAUJ,KAAKJ,OAAO;IAE5B,SAASS,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,UAAAA,WAAAA,GACtB,CAAA,GAAA,YAAA,GAAA,EAACA,SAAAA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,kFAAkF;QAClF,MAAMC,sBAAsB,CAACX,KAAKH,GAAG,IAAI,CAAC,CAACG,KAAKJ,OAAO;QACvD,MAAMgB,OAAOD,sBAAsBE,OAAAA,QAAQ,GAAGC,OAAAA,QAAQ;QACtD,MAAMC,YAAYJ,sBAAsB;YAAEK,UAAUT;QAAgB,IAAI,CAAC;QACzE,MAAMU,WAAWjB,KAAKH,GAAG,GAAA,WAAA,GACvB,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;gBAEG,OAAOqB,WAAW,cAAA,WAAA,GACjB,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,aAAa,EAAA;oBAACC,WAAWpB,KAAKqB,OAAO;qBACpC;8BACJ,CAAA,GAAA,YAAA,GAAA,EAACpB,MAAAA;oBAAM,GAAGK,KAAK;;;2BAGjB,CAAA,GAAA,YAAA,GAAA,EAACgB,qBAAAA,YAAY,EAAA;YAACC,QAAO;sBACnB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACtB,MAAAA;gBAAM,GAAGK,KAAK;;;QAInB,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACM,MAAAA;YAAM,GAAGG,SAAS;sBAAGE;;IAC/B;IAEAZ,kBAAkBmB,WAAW,GAAG;IAEhC,OAAOnB;AACT;MAEA,WAAeP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1310, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/next/src/shared/lib/app-dynamic.tsx"], "sourcesContent": ["import type React from 'react'\nimport type { JSX } from 'react'\nimport Loadable from './lazy-dynamic/loadable'\n\nimport type {\n  LoadableGeneratedOptions,\n  DynamicOptionsLoadingProps,\n  Loader,\n  LoaderComponent,\n} from './lazy-dynamic/types'\n\nexport {\n  type LoadableGeneratedOptions,\n  type DynamicOptionsLoadingProps,\n  type Loader,\n  type LoaderComponent,\n}\n\nexport type DynamicOptions<P = {}> = LoadableGeneratedOptions & {\n  loading?: () => JSX.Element | null\n  loader?: Loader<P>\n  loadableGenerated?: LoadableGeneratedOptions\n  modules?: string[]\n  ssr?: boolean\n}\n\nexport type LoadableOptions<P = {}> = DynamicOptions<P>\n\nexport type LoadableFn<P = {}> = (\n  opts: LoadableOptions<P>\n) => React.ComponentType<P>\n\nexport type LoadableComponent<P = {}> = React.ComponentType<P>\n\nexport default function dynamic<P = {}>(\n  dynamicOptions: DynamicOptions<P> | Loader<P>,\n  options?: DynamicOptions<P>\n): React.ComponentType<P> {\n  const loadableOptions: LoadableOptions<P> = {}\n\n  if (typeof dynamicOptions === 'function') {\n    loadableOptions.loader = dynamicOptions\n  }\n\n  const mergedOptions = {\n    ...loadableOptions,\n    ...options,\n  }\n\n  return Loadable({\n    ...mergedOptions,\n    modules: mergedOptions.loadableGenerated?.modules,\n  })\n}\n"], "names": ["dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loader", "Loadable", "modules", "loadableGenerated"], "mappings": ";;;+BAk<PERSON>,WAAA;;;eAAwBA;;;;mEAhCH;AAgCN,SAASA,QACtBC,cAA6C,EAC7CC,OAA2B;QAehBC;IAbX,MAAMC,kBAAsC,CAAC;IAE7C,IAAI,OAAOH,mBAAmB,YAAY;QACxCG,gBAAgBC,MAAM,GAAGJ;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOI,CAAAA,GAAAA,UAAAA,OAAQ,EAAC;QACd,GAAGH,aAAa;QAChBI,OAAO,EAAA,CAAEJ,mCAAAA,cAAcK,iBAAiB,KAAA,OAAA,KAAA,IAA/BL,iCAAiCI,OAAO;IACnD;AACF", "ignoreList": [0], "debugId": null}}]}