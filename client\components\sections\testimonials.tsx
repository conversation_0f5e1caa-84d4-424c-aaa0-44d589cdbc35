"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ScrollAnimation } from "@/components/ui/scroll-animation"
import { useTestimonials } from "@/hooks"
import { getAvatarImage } from "@/lib/images"
import {
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  User
} from "lucide-react"
import type { Testimonial } from "@/types"

// Mock testimonials data
const mockTestimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    avatar: getAvatarImage(0), // Female avatar from Unsplash
    rating: 5,
    comment: "Living here has been an amazing experience! The facilities are top-notch, the community is friendly, and the location is perfect for my daily commute. The 24/7 security gives me peace of mind, and the high-speed WiFi is perfect for my remote work.",
    date: "2024-01-15",
    roomType: "Premium Single",
    verified: true
  },
  {
    id: "2",
    name: "<PERSON>",
    avatar: getAvatarImage(1), // Male avatar from Unsplash
    rating: 5,
    comment: "As a university student, this boarding house exceeded all my expectations. The study rooms are quiet and well-lit, the shared kitchen is always clean, and the staff is incredibly helpful. Great value for money!",
    date: "2024-01-10",
    roomType: "Shared Economy",
    verified: true
  },
  {
    id: "3",
    name: "Maria Santos",
    avatar: getAvatarImage(2), // Female avatar from Unsplash
    rating: 5,
    comment: "I've been living here for 8 months now and I couldn't be happier. The double room is spacious, the balcony view is beautiful, and I've made so many friends in the common areas. Highly recommend to anyone looking for quality accommodation!",
    date: "2024-01-05",
    roomType: "Premium Double",
    verified: true
  },
  {
    id: "4",
    name: "David Chen",
    avatar: getAvatarImage(3), // Male avatar from Unsplash
    rating: 4,
    comment: "Great place for young professionals. The executive suite gives me the privacy I need while still being part of a vibrant community. The fitness corner and entertainment room are nice bonuses. Management is very responsive to any concerns.",
    date: "2023-12-28",
    roomType: "Executive Suite",
    verified: true
  },
  {
    id: "5",
    name: "Lisa Thompson",
    avatar: getAvatarImage(4), // Female avatar from Unsplash
    rating: 5,
    comment: "Moving here was the best decision I made when I started my new job in Jakarta. The location is strategic, the amenities are excellent, and the other residents are wonderful. It truly feels like home!",
    date: "2023-12-20",
    roomType: "Deluxe Single",
    verified: true
  }
]

interface TestimonialCardProps {
  testimonial: Testimonial;
  isActive: boolean;
}

function TestimonialCard({ testimonial, isActive }: TestimonialCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating 
            ? 'fill-yellow-400 text-yellow-400' 
            : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ 
        opacity: isActive ? 1 : 0.7,
        scale: isActive ? 1 : 0.95,
        y: isActive ? 0 : 10
      }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="w-full"
    >
      <Card className="p-8 h-full relative overflow-hidden">
        {/* Quote Icon */}
        <div className="absolute top-6 right-6 opacity-10">
          <Quote className="h-16 w-16 text-blue-600" />
        </div>

        <div className="relative z-10 space-y-6">
          {/* Rating */}
          <div className="flex items-center gap-1">
            {renderStars(testimonial.rating)}
          </div>

          {/* Comment */}
          <blockquote className="text-gray-700 text-lg leading-relaxed italic">
            "{testimonial.comment}"
          </blockquote>

          {/* Author Info */}
          <div className="flex items-center gap-4 pt-4 border-t border-gray-100">
            <div className="relative">
              {testimonial.avatar ? (
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <User className="h-6 w-6 text-blue-600" />
                </div>
              )}
              {testimonial.verified && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                {testimonial.verified && (
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    Verified
                  </span>
                )}
              </div>
              <div className="text-sm text-gray-500 space-y-1">
                <p>{testimonial.roomType}</p>
                <p>{formatDate(testimonial.date)}</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  )
}

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = React.useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = React.useState(true)
  const [testimonials] = React.useState(mockTestimonials)

  // Auto-play functionality
  React.useEffect(() => {
    if (!isAutoPlaying || testimonials.length <= 1) return

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, testimonials.length])

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const nextSlide = () => {
    setCurrentIndex(prev => (prev + 1) % testimonials.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const prevSlide = () => {
    setCurrentIndex(prev => (prev - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  return (
    <section id="testimonials" className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <ScrollAnimation animation="fadeIn" className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-4"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              What Our Residents Say
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our happy residents have to say 
              about their experience living with us.
            </p>
          </motion.div>
        </ScrollAnimation>

        {/* Testimonials Carousel */}
        <div className="relative max-w-4xl mx-auto">
          {/* Main Testimonial */}
          <div className="relative min-h-[400px] mb-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                <TestimonialCard 
                  testimonial={testimonials[currentIndex]} 
                  isActive={true}
                />
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSlide}
              className="p-2"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={toggleAutoPlay}
              className="p-2"
              aria-label={isAutoPlaying ? "Pause auto-play" : "Resume auto-play"}
            >
              {isAutoPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={nextSlide}
              className="p-2"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center gap-2 mb-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex 
                    ? 'bg-blue-600 scale-125' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>

          {/* Stats */}
          <ScrollAnimation animation="slideUp">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-blue-600 mb-2">4.9/5</div>
                <div className="text-gray-600">Average Rating</div>
                <div className="flex justify-center mt-2">
                  {Array.from({ length: 5 }, (_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-blue-600 mb-2">200+</div>
                <div className="text-gray-600">Happy Residents</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-blue-600 mb-2">98%</div>
                <div className="text-gray-600">Satisfaction Rate</div>
              </motion.div>
            </div>
          </ScrollAnimation>
        </div>
      </div>
    </section>
  )
}
