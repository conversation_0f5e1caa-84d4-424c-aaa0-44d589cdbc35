// Accessibility configuration and utilities for WCAG 2.1 AA compliance

export const ACCESSIBILITY_CONFIG = {
  // WCAG 2.1 AA Requirements
  colorContrast: {
    normalText: 4.5, // Minimum contrast ratio for normal text
    largeText: 3.0,  // Minimum contrast ratio for large text (18pt+ or 14pt+ bold)
    nonTextElements: 3.0 // Minimum contrast ratio for UI components
  },
  
  // Touch target sizes (minimum 44x44px for mobile)
  touchTargets: {
    minimum: 44,
    recommended: 48
  },
  
  // Animation and motion preferences
  animation: {
    defaultDuration: 300,
    reducedMotionDuration: 50,
    respectsReducedMotion: true
  },
  
  // Focus management
  focus: {
    outlineWidth: 2,
    outlineOffset: 2,
    outlineColor: '#2563eb', // Blue-600
    trapFocus: true
  },
  
  // Screen reader support
  screenReader: {
    announceChanges: true,
    liveRegionPolite: 'polite',
    liveRegionAssertive: 'assertive'
  },
  
  // Keyboard navigation
  keyboard: {
    enableTabNavigation: true,
    enableArrowNavigation: true,
    enableEscapeKey: true,
    enableEnterKey: true,
    enableSpaceKey: true
  }
}

// ARIA roles and properties
export const ARIA_ROLES = {
  // Landmark roles
  banner: 'banner',
  navigation: 'navigation',
  main: 'main',
  complementary: 'complementary',
  contentinfo: 'contentinfo',
  search: 'search',
  
  // Widget roles
  button: 'button',
  checkbox: 'checkbox',
  dialog: 'dialog',
  menu: 'menu',
  menuitem: 'menuitem',
  tab: 'tab',
  tabpanel: 'tabpanel',
  tooltip: 'tooltip',
  
  // Document structure roles
  article: 'article',
  heading: 'heading',
  list: 'list',
  listitem: 'listitem',
  
  // Live region roles
  alert: 'alert',
  status: 'status',
  log: 'log'
} as const

export const ARIA_PROPERTIES = {
  // States
  expanded: 'aria-expanded',
  selected: 'aria-selected',
  checked: 'aria-checked',
  disabled: 'aria-disabled',
  hidden: 'aria-hidden',
  pressed: 'aria-pressed',
  
  // Properties
  label: 'aria-label',
  labelledby: 'aria-labelledby',
  describedby: 'aria-describedby',
  controls: 'aria-controls',
  owns: 'aria-owns',
  live: 'aria-live',
  atomic: 'aria-atomic',
  relevant: 'aria-relevant',
  
  // Relationships
  activedescendant: 'aria-activedescendant',
  flowto: 'aria-flowto',
  
  // Widget attributes
  autocomplete: 'aria-autocomplete',
  haspopup: 'aria-haspopup',
  invalid: 'aria-invalid',
  multiline: 'aria-multiline',
  multiselectable: 'aria-multiselectable',
  orientation: 'aria-orientation',
  readonly: 'aria-readonly',
  required: 'aria-required',
  sort: 'aria-sort',
  valuemax: 'aria-valuemax',
  valuemin: 'aria-valuemin',
  valuenow: 'aria-valuenow',
  valuetext: 'aria-valuetext'
} as const

// Semantic HTML elements mapping
export const SEMANTIC_ELEMENTS = {
  header: 'header',
  nav: 'nav',
  main: 'main',
  section: 'section',
  article: 'article',
  aside: 'aside',
  footer: 'footer',
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6'
} as const

// Color contrast validation
export function validateColorContrast(
  foreground: string,
  background: string,
  isLargeText: boolean = false
): { isValid: boolean; ratio: number; required: number } {
  // This is a simplified implementation
  // In production, use a proper color contrast library like 'color-contrast'
  const requiredRatio = isLargeText 
    ? ACCESSIBILITY_CONFIG.colorContrast.largeText 
    : ACCESSIBILITY_CONFIG.colorContrast.normalText
  
  // Mock calculation - replace with actual contrast calculation
  const ratio = 4.5 // This should be calculated from actual colors
  
  return {
    isValid: ratio >= requiredRatio,
    ratio,
    required: requiredRatio
  }
}

// Heading hierarchy validation
export function validateHeadingHierarchy(headings: string[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = []
  let previousLevel = 0
  
  headings.forEach((heading, index) => {
    const level = parseInt(heading.charAt(1)) // Extract number from h1, h2, etc.
    
    if (index === 0 && level !== 1) {
      errors.push('First heading should be h1')
    }
    
    if (level > previousLevel + 1) {
      errors.push(`Heading level ${level} skips level ${previousLevel + 1}`)
    }
    
    previousLevel = level
  })
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Keyboard event handlers
export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown'
} as const

// Form validation messages
export const VALIDATION_MESSAGES = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  minLength: (min: number) => `Must be at least ${min} characters long`,
  maxLength: (max: number) => `Must be no more than ${max} characters long`,
  pattern: 'Please enter a valid format',
  numeric: 'Please enter a valid number',
  url: 'Please enter a valid URL'
} as const

// Screen reader announcements
export function announceToScreenReader(
  message: string,
  priority: 'polite' | 'assertive' = 'polite'
): void {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.setAttribute('class', 'sr-only')
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  // Remove after announcement
  setTimeout(() => {
    if (document.body.contains(announcement)) {
      document.body.removeChild(announcement)
    }
  }, 1000)
}

// Focus management utilities
export function getFocusableElements(container: HTMLElement): HTMLElement[] {
  const focusableSelectors = [
    'button:not([disabled])',
    '[href]',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ')
  
  return Array.from(container.querySelectorAll(focusableSelectors))
}

export function trapFocus(container: HTMLElement): () => void {
  const focusableElements = getFocusableElements(container)
  const firstElement = focusableElements[0]
  const lastElement = focusableElements[focusableElements.length - 1]
  
  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return
    
    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement?.focus()
        e.preventDefault()
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement?.focus()
        e.preventDefault()
      }
    }
  }
  
  container.addEventListener('keydown', handleTabKey)
  firstElement?.focus()
  
  return () => {
    container.removeEventListener('keydown', handleTabKey)
  }
}

// Motion preferences
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-contrast: high)').matches
}

// Touch target validation
export function validateTouchTarget(element: HTMLElement): {
  isValid: boolean;
  width: number;
  height: number;
  required: number;
} {
  const rect = element.getBoundingClientRect()
  const required = ACCESSIBILITY_CONFIG.touchTargets.minimum
  
  return {
    isValid: rect.width >= required && rect.height >= required,
    width: rect.width,
    height: rect.height,
    required
  }
}

// Accessibility testing utilities
export function runAccessibilityAudit(container: HTMLElement = document.body): {
  errors: string[];
  warnings: string[];
  passed: string[];
} {
  const errors: string[] = []
  const warnings: string[] = []
  const passed: string[] = []
  
  // Check for alt text on images
  const images = container.querySelectorAll('img')
  images.forEach((img, index) => {
    if (!img.alt && !img.getAttribute('aria-label')) {
      errors.push(`Image ${index + 1} missing alt text`)
    } else {
      passed.push(`Image ${index + 1} has alt text`)
    }
  })
  
  // Check for form labels
  const inputs = container.querySelectorAll('input, select, textarea')
  inputs.forEach((input, index) => {
    const hasLabel = input.getAttribute('aria-label') || 
                    input.getAttribute('aria-labelledby') ||
                    container.querySelector(`label[for="${input.id}"]`)
    
    if (!hasLabel) {
      errors.push(`Form input ${index + 1} missing label`)
    } else {
      passed.push(`Form input ${index + 1} has label`)
    }
  })
  
  // Check heading hierarchy
  const headings = Array.from(container.querySelectorAll('h1, h2, h3, h4, h5, h6'))
    .map(h => h.tagName.toLowerCase())
  
  const headingValidation = validateHeadingHierarchy(headings)
  if (!headingValidation.isValid) {
    errors.push(...headingValidation.errors)
  } else {
    passed.push('Heading hierarchy is correct')
  }
  
  return { errors, warnings, passed }
}
