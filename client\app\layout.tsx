import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { ErrorBoundary } from "@/components/error-boundary";
import { UNSPLASH_IMAGES } from "@/lib/images";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "KostHub - Platform Pencarian Kost Inovatif",
  description: "Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif untuk membantu Anda menemukan tempat tinggal yang sempurna.",
  keywords: ["kost", "sewa kamar", "tempat tinggal", "boarding house", "pencarian kost", "kost murah"],
  authors: [{ name: "KostHub Team" }],
  creator: "KostHub",
  publisher: "KostHub",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://kosthub.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "KostHub - Platform Pencarian Kost Inovatif",
    description: "Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif.",
    url: 'https://kosthub.com',
    siteName: 'KostHub',
    images: [
      {
        url: UNSPLASH_IMAGES.og.main,
        width: 1200,
        height: 630,
        alt: 'KostHub - Platform Pencarian Kost',
      },
    ],
    locale: 'id_ID',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "KostHub - Platform Pencarian Kost Inovatif",
    description: "Temukan kost impian Anda dengan mudah. Platform pencarian kost dengan preview dinamis dan fitur perbandingan interaktif.",
    images: [UNSPLASH_IMAGES.og.main],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#2563eb" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background font-sans`}
      >
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
