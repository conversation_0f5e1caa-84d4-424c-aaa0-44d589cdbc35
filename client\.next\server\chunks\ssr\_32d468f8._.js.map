{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/kost/client/components/sections/hero.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ScrollAnimation, StaggeredAnimation, ParallaxScroll } from \"@/components/ui/scroll-animation\"\nimport { \n  ArrowRight, \n  Play, \n  Star, \n  Users, \n  Shield, \n  Wifi,\n  MapPin,\n  Phone\n} from \"lucide-react\"\n\nconst heroStats = [\n  {\n    icon: Users,\n    value: \"200+\",\n    label: \"Happy Residents\"\n  },\n  {\n    icon: Star,\n    value: \"4.9\",\n    label: \"Average Rating\"\n  },\n  {\n    icon: Shield,\n    value: \"24/7\",\n    label: \"Security\"\n  },\n  {\n    icon: Wifi,\n    value: \"100%\",\n    label: \"High-Speed WiFi\"\n  }\n]\n\nconst heroFeatures = [\n  \"Modern & Clean Rooms\",\n  \"24/7 Security System\", \n  \"High-Speed Internet\",\n  \"Shared Kitchen & Lounge\",\n  \"Laundry Facilities\",\n  \"Strategic Location\"\n]\n\nexport function HeroSection() {\n  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)\n  \n  // Mock images - replace with actual boarding house images\n  const heroImages = [\n    \"/images/hero-1.jpg\",\n    \"/images/hero-2.jpg\", \n    \"/images/hero-3.jpg\"\n  ]\n\n  // Auto-rotate hero images\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)\n    }, 5000)\n    return () => clearInterval(interval)\n  }, [heroImages.length])\n\n  const handleScrollToRooms = () => {\n    const roomsSection = document.getElementById('rooms')\n    if (roomsSection) {\n      roomsSection.scrollIntoView({ behavior: 'smooth' })\n    }\n  }\n\n  const handleScrollToContact = () => {\n    const contactSection = document.getElementById('contact')\n    if (contactSection) {\n      contactSection.scrollIntoView({ behavior: 'smooth' })\n    }\n  }\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image Slider */}\n      <div className=\"absolute inset-0 z-0\">\n        {heroImages.map((image, index) => (\n          <motion.div\n            key={index}\n            className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n            style={{\n              backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url(${image})`\n            }}\n            initial={{ opacity: 0 }}\n            animate={{ \n              opacity: index === currentImageIndex ? 1 : 0,\n              scale: index === currentImageIndex ? 1.05 : 1\n            }}\n            transition={{ duration: 1, ease: \"easeInOut\" }}\n          />\n        ))}\n      </div>\n\n      {/* Parallax Background Elements */}\n      <ParallaxScroll offset={30} className=\"absolute inset-0 z-10\">\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl\" />\n        <div className=\"absolute bottom-20 right-10 w-48 h-48 bg-blue-400/10 rounded-full blur-3xl\" />\n      </ParallaxScroll>\n\n      {/* Main Content */}\n      <div className=\"relative z-20 container mx-auto px-4 py-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left space-y-8\">\n            <StaggeredAnimation staggerDelay={0.2}>\n              {/* Badge */}\n              <motion.div\n                className=\"inline-flex items-center gap-2 bg-blue-600/20 backdrop-blur-sm border border-blue-400/30 rounded-full px-4 py-2 text-blue-100\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n              >\n                <MapPin className=\"h-4 w-4\" />\n                <span className=\"text-sm font-medium\">Prime Location in Jakarta</span>\n              </motion.div>\n\n              {/* Main Headline */}\n              <motion.h1 \n                className=\"text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n              >\n                Find Your Ideal\n                <br />\n                <span className=\"bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent\">\n                  Boarding House\n                </span>\n                <br />\n                Here!\n              </motion.h1>\n\n              {/* Subtitle */}\n              <motion.p \n                className=\"text-lg sm:text-xl text-gray-200 max-w-2xl mx-auto lg:mx-0 leading-relaxed\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 }}\n              >\n                Experience comfortable living with modern amenities, 24/7 security, and a vibrant community. \n                Perfect for students and young professionals.\n              </motion.p>\n\n              {/* Features List */}\n              <motion.div \n                className=\"grid grid-cols-2 gap-3 max-w-md mx-auto lg:mx-0\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.6 }}\n              >\n                {heroFeatures.map((feature, index) => (\n                  <div key={index} className=\"flex items-center gap-2 text-gray-200\">\n                    <div className=\"w-2 h-2 bg-blue-400 rounded-full\" />\n                    <span className=\"text-sm\">{feature}</span>\n                  </div>\n                ))}\n              </motion.div>\n\n              {/* CTA Buttons */}\n              <motion.div \n                className=\"flex flex-col sm:flex-row gap-4 pt-4\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.8 }}\n              >\n                <Button \n                  size=\"lg\" \n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300\"\n                  onClick={handleScrollToRooms}\n                >\n                  View Rooms\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Button>\n                <Button \n                  size=\"lg\" \n                  variant=\"outline\" \n                  className=\"border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-3 text-lg font-semibold\"\n                  onClick={handleScrollToContact}\n                >\n                  <Phone className=\"mr-2 h-5 w-5\" />\n                  Contact Us\n                </Button>\n              </motion.div>\n            </StaggeredAnimation>\n          </div>\n\n          {/* Right Content - Stats */}\n          <div className=\"lg:justify-self-end\">\n            <motion.div \n              className=\"grid grid-cols-2 gap-6 max-w-md mx-auto\"\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 1, delay: 0.5 }}\n            >\n              {heroStats.map((stat, index) => (\n                <motion.div\n                  key={index}\n                  className=\"bg-white/10 backdrop-blur-md rounded-2xl p-6 text-center border border-white/20 hover:bg-white/20 transition-all duration-300\"\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}\n                  whileHover={{ scale: 1.05, y: -5 }}\n                >\n                  <div className=\"flex flex-col items-center space-y-3\">\n                    <div className=\"p-3 bg-blue-500/20 rounded-full\">\n                      <stat.icon className=\"h-6 w-6 text-blue-400\" />\n                    </div>\n                    <div className=\"space-y-1\">\n                      <div className=\"text-2xl font-bold text-white\">\n                        {stat.value}\n                      </div>\n                      <div className=\"text-sm text-gray-300 font-medium\">\n                        {stat.label}\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div \n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 1.2 }}\n      >\n        <motion.div\n          className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <motion.div\n            className=\"w-1 h-3 bg-white/70 rounded-full mt-2\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          />\n        </motion.div>\n      </motion.div>\n\n      {/* Image Indicators */}\n      <div className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2\">\n        {heroImages.map((_, index) => (\n          <button\n            key={index}\n            className={`w-3 h-3 rounded-full transition-all duration-300 ${\n              index === currentImageIndex \n                ? 'bg-blue-400 scale-125' \n                : 'bg-white/50 hover:bg-white/70'\n            }`}\n            onClick={() => setCurrentImageIndex(index)}\n            aria-label={`View image ${index + 1}`}\n          />\n        ))}\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAiBA,MAAM,YAAY;IAChB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEjE,0DAA0D;IAC1D,MAAM,aAAa;QACjB;QACA;QACA;KACD;IAED,0BAA0B;IAC1B,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,WAAW,YAAY;YAC3B,qBAAqB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,WAAW,MAAM;QAC/D,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,WAAW,MAAM;KAAC;IAEtB,MAAM,sBAAsB;QAC1B,MAAM,eAAe,SAAS,cAAc,CAAC;QAC7C,IAAI,cAAc;YAChB,aAAa,cAAc,CAAC;gBAAE,UAAU;YAAS;QACnD;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,iBAAiB,SAAS,cAAc,CAAC;QAC/C,IAAI,gBAAgB;YAClB,eAAe,cAAc,CAAC;gBAAE,UAAU;YAAS;QACrD;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,6DAA6D,EAAE,MAAM,CAAC,CAAC;wBAC3F;wBACA,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BACP,SAAS,UAAU,oBAAoB,IAAI;4BAC3C,OAAO,UAAU,oBAAoB,OAAO;wBAC9C;wBACA,YAAY;4BAAE,UAAU;4BAAG,MAAM;wBAAY;uBAVxC;;;;;;;;;;0BAgBX,8OAAC,wIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAI,WAAU;;kCACpC,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAA,CAAA,qBAAkB;gCAAC,cAAc;;kDAEhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAIxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;4CACzC;0DAEC,8OAAC;;;;;0DACD,8OAAC;gDAAK,WAAU;0DAA2E;;;;;;0DAG3F,8OAAC;;;;;4CAAK;;;;;;;kDAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDACzC;;;;;;kDAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDAEvC,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;+CAFnB;;;;;;;;;;kDAQd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,8OAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,SAAS;;oDACV;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,8OAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;;kEAET,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;0CAErC,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;uCAhBZ;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4BjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAE5C,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBAAC;wBAClC,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;;;;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,GAAG,sBAClB,8OAAC;wBAEC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,oBACN,0BACA,iCACJ;wBACF,SAAS,IAAM,qBAAqB;wBACpC,cAAY,CAAC,WAAW,EAAE,QAAQ,GAAG;uBAPhC;;;;;;;;;;;;;;;;AAajB", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/users.js", "sources": ["file:///D:/Vicky/project%20baru/kost/client/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,OAAA;QAAS,CAAA;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}