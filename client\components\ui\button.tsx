import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { motion } from "framer-motion"

import { cn } from "@/lib/utils"
import { buttonHoverVariants } from "@/utils/animations"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

interface ButtonProps extends React.ComponentProps<"button">, VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
}

function Button({
  className,
  variant,
  size,
  asChild = false,
  loading = false,
  loadingText = "Loading...",
  children,
  disabled,
  ...props
}: ButtonProps) {
  const isDisabled = disabled || loading
  const buttonContent = (
    <>
      {loading && (
        <motion.div
          className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
          aria-hidden="true"
        />
      )}
      <span className={loading ? "opacity-70" : ""}>
        {loading ? loadingText : children}
      </span>
    </>
  )

  if (asChild) {
    // Extract only the props that Slot can handle
    const {
      onDrag, onDragEnd, onDragStart, onAnimationStart, onAnimationEnd,
      onTransitionEnd, ...slotProps
    } = props

    return (
      <Slot
        className={cn(buttonVariants({ variant, size, className }))}
        aria-disabled={isDisabled}
        {...slotProps}
      >
        {buttonContent}
      </Slot>
    )
  }

  // For motion.button, we need to filter out HTML-specific events
  // that conflict with Framer Motion's events
  const {
    onDrag, onDragEnd, onDragStart, onDragEnter, onDragExit, onDragLeave,
    onDragOver, onDrop, onAnimationStart, onAnimationEnd, onTransitionEnd,
    ...motionProps
  } = props

  return (
    <motion.button
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      variants={buttonHoverVariants}
      initial="rest"
      whileHover={!isDisabled ? "hover" : "rest"}
      whileTap={!isDisabled ? "tap" : "rest"}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      {...motionProps}
    >
      {buttonContent}
    </motion.button>
  )
}

export { Button, buttonVariants }
