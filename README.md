# BoardingHouse Landing Page

A modern, responsive boarding house landing page built with React, TypeScript, Tailwind CSS, and Framer Motion. This project follows WCAG 2.1 AA accessibility standards and implements comprehensive performance optimizations.

## 🌟 Features

### ✨ Modern Design
- **Responsive Design**: Works seamlessly across all device sizes (mobile, tablet, desktop)
- **Modern UI Components**: Built with shadcn/ui and custom components
- **Smooth Animations**: Powered by Framer Motion with performance optimizations
- **Glass Morphism Effects**: Modern visual effects with backdrop blur
- **Dark Mode Support**: Automatic theme switching capabilities

### 🚀 Performance Optimizations
- **Lazy Loading**: Images and components load on demand
- **Code Splitting**: Route-based and component-based splitting with React.Suspense
- **Image Optimization**: WebP/AVIF support with responsive images
- **Bundle Optimization**: Tree shaking and dynamic imports
- **Prefers-Reduced-Motion**: Respects user motion preferences

### ♿ Accessibility (WCAG 2.1 AA Compliant)
- **Keyboard Navigation**: Full keyboard accessibility support
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Proper focus trapping and visual indicators
- **Color Contrast**: Minimum 4.5:1 contrast ratio for normal text
- **Touch Targets**: Minimum 44px touch targets for mobile
- **Skip Links**: Navigation shortcuts for screen readers

### 🏠 Boarding House Specific Features
- **Room Showcase**: Interactive room cards with detailed information
- **Facilities Grid**: Comprehensive amenities display
- **Testimonials Carousel**: Customer reviews with auto-play
- **Contact Integration**: Multiple contact methods and forms
- **Virtual Tour Ready**: Prepared for 360° room tours

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.2 with App Router
- **Language**: TypeScript 5.x
- **Styling**: Tailwind CSS 4.x
- **Animations**: Framer Motion
- **UI Components**: shadcn/ui + Custom components
- **Icons**: Lucide React
- **Performance**: React.Suspense, Intersection Observer API
- **Accessibility**: Custom accessibility utilities and ARIA support

## 📁 Project Structure

```
client/
├── app/                          # Next.js App Router
│   ├── globals.css              # Global styles with accessibility
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Home page
├── components/
│   ├── ui/                      # Reusable UI components
│   │   ├── button.tsx           # Enhanced button with animations
│   │   ├── card.tsx             # Flexible card component
│   │   ├── modal.tsx            # Accessible modal component
│   │   ├── input.tsx            # Form input with validation
│   │   ├── loading.tsx          # Loading states and skeletons
│   │   └── scroll-animation.tsx # Scroll-triggered animations
│   ├── sections/                # Page sections
│   │   ├── hero.tsx             # Hero section with image slider
│   │   ├── rooms.tsx            # Room listings with modal
│   │   ├── facilities.tsx       # Facilities grid
│   │   └── testimonials.tsx     # Customer testimonials
│   ├── navigation.tsx           # Header navigation
│   ├── footer.tsx               # Footer with contact info
│   └── boarding-house-landing.tsx # Main landing page component
├── types/                       # TypeScript type definitions
├── hooks/                       # Custom React hooks
├── utils/                       # Utility functions
│   ├── animations.ts            # Framer Motion variants
│   ├── accessibility.ts         # Accessibility helpers
│   └── performance.ts           # Performance optimization utilities
└── lib/                         # Library configurations
    └── accessibility.ts         # WCAG compliance utilities
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kost/client
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm run start
```

## 🎨 Customization

### Colors and Theming
The project uses CSS custom properties for theming. Update colors in `app/globals.css`:

```css
:root {
  --primary: oklch(0.478 0.166 258.267);
  --secondary: oklch(0.97 0.005 106.423);
  /* ... other colors */
}
```

### Content Updates
1. **Room Data**: Update room information in `components/sections/rooms.tsx`
2. **Facilities**: Modify facilities list in `components/sections/facilities.tsx`
3. **Testimonials**: Update customer reviews in `components/sections/testimonials.tsx`
4. **Contact Info**: Update contact details in `components/footer.tsx`

### Images
Replace placeholder images in the `public/images/` directory:
- Hero images: `hero-1.jpg`, `hero-2.jpg`, `hero-3.jpg`
- Room images: `room-single-1.jpg`, `room-double-1.jpg`, etc.
- Avatar images: `avatar-sarah.jpg`, `avatar-ahmad.jpg`, etc.

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: > 1024px

## ♿ Accessibility Features

### Keyboard Navigation
- **Tab**: Navigate through interactive elements
- **Enter/Space**: Activate buttons and links
- **Escape**: Close modals and dropdowns
- **Arrow Keys**: Navigate within components

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Live region announcements
- Skip navigation links

### Visual Accessibility
- High contrast mode support
- Focus visible indicators
- Reduced motion support
- Minimum touch target sizes (44px)

## 🔧 Performance Features

### Loading Optimizations
- Lazy loading for images and components
- Intersection Observer for scroll animations
- React.Suspense for code splitting
- Preloading of critical resources

### Animation Optimizations
- Respects `prefers-reduced-motion`
- GPU-accelerated animations
- Optimized animation timing
- Conditional animation loading

## 🧪 Testing

### Accessibility Testing
Run the built-in accessibility audit:
```javascript
import { runAccessibilityAudit } from '@/lib/accessibility'
const results = runAccessibilityAudit()
console.log(results)
```

### Performance Testing
- Use Chrome DevTools Lighthouse
- Monitor Core Web Vitals
- Test on various devices and network conditions

## 📦 Deployment

### Vercel (Recommended)
```bash
npm run build
# Deploy to Vercel
```

### Other Platforms
The project is compatible with:
- Netlify
- AWS Amplify
- Railway
- Any Node.js hosting platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the component library
- [Framer Motion](https://www.framer.com/motion/) for animations
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Lucide](https://lucide.dev/) for icons
- [Next.js](https://nextjs.org/) for the framework

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Built with ❤️ for comfortable living**
