@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.99 0.005 106.423);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.478 0.166 258.267);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.005 106.423);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0.005 106.423);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.646 0.222 41.116);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0.005 106.423);
  --input: oklch(0.922 0.005 106.423);
  --ring: oklch(0.478 0.166 258.267);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.478 0.166 258.267);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0.005 106.423);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0.005 106.423);
  --sidebar-ring: oklch(0.478 0.166 258.267);

  /* Modern Professional Kost Theme Variables */
  --kost-primary: oklch(0.55 0.15 240); /* Professional blue */
  --kost-secondary: oklch(0.65 0.12 160); /* Sophisticated emerald */
  --kost-accent: oklch(0.70 0.08 200); /* Subtle blue accent */
  --kost-success: oklch(0.65 0.12 160); /* Success green */
  --kost-warning: oklch(0.75 0.15 80); /* Warning amber */
  --kost-info: oklch(0.60 0.10 220); /* Info blue */
  --kost-neutral: oklch(0.50 0.02 240); /* Neutral gray-blue */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Kost Card Hover Effects */
  .kost-card {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  .kost-card-image {
    @apply transition-transform duration-300 hover:scale-105;
  }

  /* Search Bar Styling */
  .search-bar {
    @apply relative flex items-center w-full max-w-2xl mx-auto;
  }

  /* Filter Panel */
  .filter-panel {
    @apply space-y-4 p-4 bg-card rounded-lg border;
  }

  /* Price Range Styling */
  .price-range {
    @apply flex items-center justify-between text-sm text-muted-foreground;
  }

  /* Comparison Table */
  .comparison-table {
    @apply w-full border-collapse bg-card rounded-lg overflow-hidden;
  }

  /* Modern Hero Section */
  .hero-modern-gradient {
    background: linear-gradient(135deg,
      oklch(0.15 0.02 240) 0%,
      oklch(0.18 0.03 220) 50%,
      oklch(0.15 0.02 240) 100%);
  }

  /* Animated background elements */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  @keyframes pulse-glow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 4s ease-in-out infinite;
  }

  /* Glass morphism effect */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Modern card hover effects */
  .modern-card {
    @apply transition-all duration-500 ease-out;
    transform-style: preserve-3d;
  }

  .modern-card:hover {
    @apply shadow-2xl;
    transform: translateY(-8px) rotateX(5deg);
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(135deg, #3b82f6, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Custom Scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-muted rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/80;
  }

  /* Accessibility Improvements */

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem 1rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    @apply bg-blue-600 text-white rounded z-50;
  }

  /* Focus visible styles */
  .focus-visible {
    @apply outline-2 outline-offset-2 outline-blue-600;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --background: #ffffff;
      --foreground: #000000;
      --primary: #0000ff;
      --border: #000000;
    }

    .dark {
      --background: #000000;
      --foreground: #ffffff;
      --primary: #00ffff;
      --border: #ffffff;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .animate-float,
    .animate-pulse-glow {
      animation: none !important;
    }
  }

  /* Focus management for modals and dropdowns */
  .focus-trap {
    position: relative;
  }

  .focus-trap:focus-within {
    @apply ring-2 ring-blue-600 ring-offset-2;
  }

  /* Skip links */
  .skip-link {
    @apply sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded;
  }

  /* Keyboard navigation indicators */
  .keyboard-navigation button:focus-visible,
  .keyboard-navigation a:focus-visible,
  .keyboard-navigation input:focus-visible,
  .keyboard-navigation select:focus-visible,
  .keyboard-navigation textarea:focus-visible {
    @apply outline-2 outline-offset-2 outline-blue-600;
  }

  /* Color contrast improvements */
  .text-contrast-high {
    color: contrast(var(--background) vs #000000, #ffffff);
  }

  /* Touch target size compliance (minimum 44px) */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    @apply flex items-center justify-center;
  }

  /* Error states with proper contrast */
  .error-state {
    @apply border-red-600 bg-red-50 text-red-900;
  }

  .dark .error-state {
    @apply border-red-400 bg-red-900/20 text-red-100;
  }

  /* Success states with proper contrast */
  .success-state {
    @apply border-green-600 bg-green-50 text-green-900;
  }

  .dark .success-state {
    @apply border-green-400 bg-green-900/20 text-green-100;
  }

  /* Loading states with proper accessibility */
  .loading-state {
    @apply relative overflow-hidden;
  }

  .loading-state::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading-shimmer 1.5s infinite;
  }

  @keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  @media (prefers-reduced-motion: reduce) {
    .loading-state::after {
      animation: none;
      background: rgba(255, 255, 255, 0.1);
      left: 0;
    }
  }
}
