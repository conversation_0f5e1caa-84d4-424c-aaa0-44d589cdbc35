import { Metadata } from "next"
import { BoardingHouseLanding, BoardingHouseErrorBoundary } from "@/components/boarding-house-landing"

export const metadata: Metadata = {
  title: "BoardingHouse - Find Your Ideal Boarding House",
  description: "Experience comfortable living with modern amenities, 24/7 security, and a vibrant community. Perfect for students and young professionals in Jakarta.",
  keywords: ["boarding house", "kost", "accommodation", "Jakarta", "student housing", "modern facilities"],
  authors: [{ name: "BoardingHouse Team" }],
  openGraph: {
    title: "BoardingHouse - Find Your Ideal Boarding House",
    description: "Experience comfortable living with modern amenities, 24/7 security, and a vibrant community.",
    type: "website",
    locale: "en_US",
    siteName: "BoardingHouse",
  },
  twitter: {
    card: "summary_large_image",
    title: "BoardingHouse - Find Your Ideal Boarding House",
    description: "Experience comfortable living with modern amenities, 24/7 security, and a vibrant community.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
}

export default function Home() {
  return (
    <BoardingHouseErrorBoundary>
      <BoardingHouseLanding />
    </BoardingHouseErrorBoundary>
  )
}
